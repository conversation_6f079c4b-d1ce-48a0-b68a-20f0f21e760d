<?php

return [
    // Genel
    'app_name' => 'Vox eSIM',
    'home' => 'Home',
    'countries' => 'Countries',
    'login' => 'Login',
    'register' => 'Register',
    'logout' => 'Logout',
    'profile' => 'Profile',
    'search_placeholder' => 'Search for a country...',
    'select_country' => 'Select a Country',
    'view_plans' => 'View Plans',
    'buy_now' => 'Buy Now',
    'explore_other_countries' => 'Explore Other Countries',
    
    // Ana Sayfa
    'hero_title' => 'Global Connectivity with Vox eSIM',
    'hero_subtitle' => 'Stay connected anywhere in the world with our affordable and reliable eSIM plans. No physical SIM card needed - just scan, activate, and connect!',
    'find_destination' => 'Find Your Destination',
    'best_selling_plans' => 'Best Selling eSIM Plans',
    'best_seller' => 'Best Seller',
    'best_value' => 'Best Value',
    'popular' => 'Popular',
    'coverage_in' => 'Coverage in',
    'valid_for' => 'Valid for',
    'days' => 'days',
    'supported_countries' => 'eSIM Supported Countries',
    'no_countries_found' => 'No countries found matching your search. Please try a different search term.',
    
    // Nasıl Çalışır
    'how_it_works' => 'How It Works',
    'step_1_title' => 'Purchase',
    'step_1_desc' => 'Choose your destination and select the eSIM plan that fits your needs.',
    'step_2_title' => 'Activate',
    'step_2_desc' => 'Scan the QR code we send you to install the eSIM on your device.',
    'step_3_title' => 'Connect',
    'step_3_desc' => 'Turn on your eSIM when you arrive at your destination and start browsing!',
    
    // Destek
    'need_help' => 'Need Help?',
    'contact_support' => 'Contact Our Support Team',
    'support_desc' => 'Our customer support team is available 24/7 to assist you with any questions or issues you may have.',
    'faq' => 'Frequently Asked Questions',
    'what_is_esim' => 'What is an eSIM?',
    'what_is_esim_answer' => 'An eSIM (embedded SIM) is a digital SIM that allows you to activate a cellular plan without having to use a physical SIM card.',
    'is_device_compatible' => 'Is my device eSIM compatible?',
    'is_device_compatible_answer' => 'Most newer smartphones support eSIM technology, including iPhone XS and newer, Google Pixel 3 and newer, and many Samsung Galaxy models.',
    
    // Ülke Sayfası
    'esim_plans' => 'eSIM Plans',
    'choose_plan' => 'Choose the perfect eSIM plan for your trip to',
    'data' => 'Data',
    'duration' => 'Duration',
    'price' => 'Price',
    'no_packages' => 'No eSIM packages available for',
    'check_back' => 'at the moment. Please check back later or explore other countries.',
    'why_choose' => 'Why Choose Vox eSIM for',
    'instant_activation' => 'Instant Activation',
    'instant_activation_desc' => 'Get connected immediately after purchase with our instant activation process.',
    'reliable_coverage' => 'Reliable Coverage',
    'reliable_coverage_desc' => 'Enjoy excellent network coverage throughout',
    'affordable_pricing' => 'Affordable Pricing',
    'affordable_pricing_desc' => 'Get the best value for your money with our competitive pricing.',
    '24_7_support' => '24/7 Support',
    '24_7_support_desc' => 'Our dedicated support team is always ready to assist you.',
    
    // Ödeme Sayfası
    'checkout' => 'Checkout',
    'payment_details' => 'Payment Details',
    'personal_info' => 'Personal Information',
    'name' => 'Full Name',
    'email' => 'Email Address',
    'payment_method' => 'Payment Method',
    'credit_card' => 'Credit Card',
    'crypto' => 'Cryptocurrency',
    'rapyd' => 'Rapyd',
    'complete_purchase' => 'Complete Purchase',
    'order_summary' => 'Order Summary',
    'selected_plan' => 'Selected Plan',
    'total' => 'Total',
    
    // Başarı Sayfası
    'purchase_successful' => 'Purchase Successful!',
    'thank_you' => 'Thank you for your purchase!',
    'esim_details' => 'Your eSIM details have been sent to your email address.',
    'scan_qr' => 'Scan the QR code below to install your eSIM:',
    'activation_instructions' => 'Activation Instructions',
    'back_to_home' => 'Back to Home',
    
    // Hata Mesajları
    'error' => 'Error',
    'country_not_found' => 'The specified country could not be found.',
    'payment_cancelled' => 'Payment was cancelled.',
    'payment_failed' => 'Payment failed. Please try again.',
    'general_error' => 'An error occurred. Please contact customer support.',
];
