@extends('layouts.app')

@section('content')
<!-- Success Header -->
<section class="py-4 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                <li class="breadcrumb-item active">Payment Successful</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Success Content -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 text-center">
                <div class="checkout-container">
                    <div class="success-animation mb-4">
                        <div class="success-checkmark">
                            <div class="check-icon">
                                <span class="icon-line line-tip"></span>
                                <span class="icon-line line-long"></span>
                                <div class="icon-circle"></div>
                                <div class="icon-fix"></div>
                            </div>
                        </div>
                    </div>

                    <h1 class="mb-3">Payment Successful!</h1>
                    <p class="lead mb-4">
                        @if(isset($message))
                            {{ $message }}
                        @else
                            Your order has been successfully completed.
                        @endif
                    </p>

                    <div class="alert alert-info mb-4" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        @if(isset($pendingMessage))
                            {{ $pendingMessage }}
                        @else
                            Your eSIM details have been sent to your email address.
                            Please check your inbox (and spam folder).
                        @endif
                    </div>

                    <div class="order-details p-4 mb-4 bg-light rounded text-start">
                        <h4 class="mb-3">Order Details</h4>
                        <div class="row mb-2">
                            <div class="col-6 text-muted">Order Number:</div>
                            <div class="col-6 fw-bold">{{ $orderNo ?? 'N/A' }}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-6 text-muted">Date:</div>
                            <div class="col-6">{{ now()->format('F j, Y') }}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-6 text-muted">Payment Method:</div>
                            <div class="col-6">
                                {{ isset($paymentMethod) && $paymentMethod == 'rapyd' ? 'Rapyd Pay' : 'Credit Card' }}
                            </div>
                        </div>
                    </div>

                    <div class="next-steps mb-4">
                        <h4 class="mb-3">Next Steps</h4>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="bg-white p-3 rounded shadow-sm h-100">
                                    <div class="mb-3">
                                        <i class="fas fa-envelope fa-2x text-primary"></i>
                                    </div>
                                    <h5>Check Your Email</h5>
                                    <p class="small">We've sent your eSIM QR code to your email address.</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="bg-white p-3 rounded shadow-sm h-100">
                                    <div class="mb-3">
                                        <i class="fas fa-qrcode fa-2x text-primary"></i>
                                    </div>
                                    <h5>Scan QR Code</h5>
                                    <p class="small">Use your phone to scan the QR code to install the eSIM.</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="bg-white p-3 rounded shadow-sm h-100">
                                    <div class="mb-3">
                                        <i class="fas fa-wifi fa-2x text-primary"></i>
                                    </div>
                                    <h5>Connect</h5>
                                    <p class="small">Activate your eSIM when you arrive at your destination.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <a href="{{ route('home') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-home me-2"></i> Return to Home Page
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection