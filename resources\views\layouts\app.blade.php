<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-16921653689"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'AW-16921653689');
    </script>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="Vox eSIM - Global connectivity with affordable and reliable eSIM plans for travelers worldwide.">
    <meta name="keywords" content="eSIM, travel, data, roaming, international, connectivity">

    <title>{{ config('app.name', 'Vox eSIM') }}</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('images/esimlogo.png') }}" type="image/png">
    <link rel="shortcut icon" href="{{ asset('images/esimlogo.png') }}" type="image/png">

    <!-- Modern Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link href="{{ asset('css/style.css') }}" rel="stylesheet">

    @stack('styles')
</head>
<body>
    <div id="app">
        <!-- Header -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
            <div class="container">
                <!-- Logo -->
                <a class="navbar-brand" href="{{ url('/') }}">
{{--                     <img src="{{ asset('images/logo.png') }}" alt="Vox eSIM Logo" class="img-fluid">
 --}}                    <span>Vox eSIM</span>
                </a>

                <!-- Mobile Menu Toggle -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Main Navigation -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url('/') }}">{{ __('messages.home') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#best-sellers">{{ __('messages.best_selling_plans') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#how-it-works">{{ __('messages.how_it_works') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#countries">{{ __('messages.countries') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#support">{{ __('messages.need_help') }}</a>
                        </li>
                    </ul>

                    <!-- Right Side Navigation -->
                    <ul class="navbar-nav ms-auto">
                   {{-- <li class="nav-item dropdown language-switcher">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <div class="current-lang">
                                    @php
                                        $locale = app()->getLocale();
                                        $flagMap = [
                                            'en' => ['file' => 'en.svg', 'name' => 'English'],
                                            'tr' => ['file' => 'tr.svg', 'name' => 'Türkçe'],
                                            'de' => ['file' => 'de.svg', 'name' => 'Deutsch'],
                                            'es' => ['file' => 'es.svg', 'name' => 'Español'],
                                            'fr' => ['file' => 'fr.svg', 'name' => 'Français'],
                                            'it' => ['file' => 'it.svg', 'name' => 'Italiano'],
                                            'ja' => ['file' => 'jp.svg', 'name' => '日本語'],
                                            'ko' => ['file' => 'kr.svg', 'name' => '한국어'],
                                            'zh' => ['file' => 'cn.svg', 'name' => '中文'],
                                            'ru' => ['file' => 'ru.svg', 'name' => 'Русский'],
                                            'pt' => ['file' => 'pt.svg', 'name' => 'Português'],
                                            'ar' => ['file' => 'ae.svg', 'name' => 'العربية']
                                        ];

                                        $flag = isset($flagMap[$locale]) ? $flagMap[$locale] : $flagMap['en'];
                                    @endphp
                                    <img src="{{ asset('images/flags/' . $flag['file']) }}" alt="{{ $flag['name'] }}" width="20">
                                    <span class="lang-name">{{ $flag['name'] }}</span>
                                </div>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end">
                                <a class="dropdown-item language-option" href="{{ route('language.switch', 'en') }}">
                                    <img src="{{ asset('images/flags/en.svg') }}" alt="English">
                                    <span class="lang-name">English</span>
                                </a>
                                 Other language options removed
                            </div>
                        </li> --}}

                        @guest
                            @if (Route::has('login'))
                                <li class="nav-item">
                                    <a class="nav-link login-btn" href="{{ route('login') }}">{{ __('messages.login') }}</a>
                                </li>
                            @endif
                        @else
                            <li class="nav-item dropdown user-dropdown">
                                <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    <i class="fas fa-user-circle me-1"></i> {{ explode('@', Auth::user()->email)[0] }}
                                </a>

                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item" href="{{ route('profile.dashboard') }}">
                                        <i class="fas fa-tachometer-alt me-2"></i> {{ __('messages.profile') }}
                                    </a>

                                    <a class="dropdown-item" href="{{ route('logout') }}"
                                       onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                        <i class="fas fa-sign-out-alt me-2"></i> {{ __('messages.logout') }}
                                    </a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                    </form>
                                </div>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main>
            @yield('content')
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="row">
                    <!-- Logo and Description -->
                    <div class="col-lg-4 mb-4">
                        <div class="footer-logo">
                            <img src="{{ asset('images/esimlogo.png') }}" alt="Vox eSIM" class="img-fluid">
                        </div>
                        <p>Vox eSIM provides affordable and reliable eSIM solutions for travelers worldwide. Stay connected wherever you go with our global coverage.</p>
                        <div class="app-stores mt-3">
                            <a href="#" class="app-store-btn apple-btn me-3" data-bs-toggle="modal" data-bs-target="#comingSoonModal">
                                <i class="fab fa-apple"></i> App Store
                            </a>
                            <a href="#" class="app-store-btn google-btn" data-bs-toggle="modal" data-bs-target="#comingSoonModal">
                                <i class="fab fa-google-play"></i> Google Play
                            </a>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="col-lg-2 col-md-4 mb-4">
                        <h5>Quick Links</h5>
                        <ul class="footer-links">
                            <li><a href="{{ url('/') }}">Home</a></li>
                            <li><a href="#best-sellers">Best Sellers</a></li>
                            <li><a href="#how-it-works">How It Works</a></li>
                            <li><a href="#countries">Countries</a></li>
                        </ul>
                    </div>

                    <!-- Support -->
                    <div class="col-lg-3 col-md-4 mb-4">
                        <h5>Support</h5>
                        <ul class="footer-links">
                            <li><a href="#">FAQ</a></li>
                            <li><a href="#">Contact Us</a></li>
                            <li><a href="#">Privacy Policy</a></li>
                            <li><a href="#">Terms of Service</a></li>
                        </ul>
                    </div>

                    <!-- Contact -->
                    <div class="col-lg-3 col-md-4 mb-4">
                        <h5>Contact Us</h5>
                        <ul class="footer-links">
                            <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                            <li><i class="fas fa-map-marker-alt me-2"></i> Sepapaja 6, Tallinn 15551, Estonia</li>
                        </ul>
                    </div>
                </div>

                <!-- Copyright -->
                <div class="footer-bottom">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <p class="mb-0">&copy; {{ date('Y') }} Vox eSIM. All rights reserved.</p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <p class="mb-0">Designed with <i class="fas fa-heart text-accent"></i> for global connectivity</p>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="{{ asset('js/main.js') }}"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    @stack('scripts')
</body>
    <!-- Coming Soon Modal -->
    <div class="modal fade" id="comingSoonModal" tabindex="-1" aria-labelledby="comingSoonModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="comingSoonModalLabel">Coming Soon!</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <div class="coming-soon-icon mb-3">
                        <img src="{{ asset('images/logo.png') }}" alt="Vox eSIM" class="img-fluid" style="max-height: 60px;">
                    </div>
                    <h4>Our mobile app is coming soon!</h4>
                    <p class="mb-0">We're currently only selling through our website, but our mobile app will be available in the near future. Stay tuned for updates!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Got it</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Cookie Consent Popup -->
    <div id="cookie-consent" class="cookie-consent">
        <div class="cookie-consent-header">
            <div class="cookie-consent-icon">
                <i class="fas fa-cookie-bite"></i>
            </div>
            <h4>Çerez Kullanımı</h4>
        </div>
        <div class="cookie-consent-message">
            <p>Size daha iyi bir deneyim sunmak için çerezleri kullanıyoruz. Devam ederek çerez kullanımını kabul etmiş olursunuz.</p>
        </div>
        <div class="cookie-consent-actions">
            <button id="accept-cookies" class="cookie-consent-btn cookie-accept-btn">Kabul Et</button>
            <button id="decline-cookies" class="cookie-consent-btn cookie-decline-btn">Reddet</button>
            <a href="{{ route('cookie.policy') }}" class="cookie-settings-btn">Detaylar</a>
        </div>
    </div>

    <!-- CSS ve JS dosyalarını ekleyin -->
    <link rel="stylesheet" href="{{ asset('css/cookie-consent.css') }}">
    <link rel="stylesheet" href="{{ asset('css/ai-assistant.css') }}">
    <script src="{{ asset('js/cookie-consent.js') }}"></script>
    <script src="{{ asset('js/ai-assistant.js') }}"></script>
</body>
</html>














