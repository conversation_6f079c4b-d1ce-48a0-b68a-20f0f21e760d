<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class RapydService
{
    private $baseUrl;
    private $accessKey;
    private $secretKey;

    public function __construct()
    {
        $this->baseUrl = 'https://sandboxapi.rapyd.net';
        $this->accessKey = config('services.rapyd.access_key');
        $this->secretKey = config('services.rapyd.secret_key');
    }

    public function createCustomer($name, $email)
    {
        $path = '/v1/customers';
        $body = [
            'name' => $name,
            'email' => $email
        ];
        return $this->makeRequest('post', $path, $body);
    }

    public function createCheckout($data)
    {
        $path = '/v1/checkout';
        return $this->makeRequest('post', $path, $data);
    }

    /**
     * Recursively convert numeric values to strings to avoid Rapyd API signature issues
     *
     * @param array &$data The array to process
     * @return void
     */
    private function convertNumericValuesToString(&$data)
    {
        foreach ($data as $key => &$value) {
            if (is_numeric($value)) {
                // Convert numeric values to strings to avoid signature issues
                $value = (string)$value;
            } elseif (is_array($value)) {
                $this->convertNumericValuesToString($value);
            }
        }
    }

    protected function makeRequest($method, $path, $body = [])
    {
        // Convert numeric values to strings to avoid signature issues
        $this->convertNumericValuesToString($body);

        // JSON string'i sıkıştırılmış şekilde oluştur (boşlukları kaldır)
        // Rapyd API imza sorunlarını önlemek için JSON_PRESERVE_ZERO_FRACTION bayrağını kaldırıyoruz
        $bodyString = json_encode($body, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        // Salt ve timestamp oluştur
        $salt = $this->generateSalt(12);
        $timestamp = time();

        // İmza oluştur (Python koduna tam uyumlu)
        $toSign = $method . $path . $salt . (string)$timestamp . $this->accessKey . $this->secretKey . $bodyString;
        $hmac = hash_hmac('sha256', $toSign, $this->secretKey); // Hex string
        $signature = base64_url_encode($hmac); // Hex string'i direkt encode et (Python'daki str.encode eşdeğeri)

        // Header'ları hazırla
        $headers = [
            'access_key' => $this->accessKey,
            'salt' => $salt,
            'timestamp' => (string)$timestamp,
            'signature' => $signature,
            'Content-Type' => 'application/json',
            'idempotency' => $timestamp . $salt
        ];

        // Loglama
        Log::debug('Rapyd Signature Generation', [
            'method' => $method,
            'urlPath' => $path,
            'salt' => $salt,
            'timestamp' => $timestamp,
            'accessKey' => $this->accessKey,
            'bodyString' => $bodyString,
            'toSign' => $toSign,
            'hmac' => $hmac, // Hex çıktısını logla
            'signature' => $signature // Son imzayı logla
        ]);
        Log::debug('Rapyd API Request', [
            'method' => $method,
            'url' => $this->baseUrl . $path,
            'headers' => $headers,
            'body' => $body
        ]);

        // İstek gönder
        $response = Http::withHeaders($headers)->post($this->baseUrl . $path, $body);

        // Loglama
        if ($response->status() !== 200) {
            Log::error('Rapyd API Error', [
                'status' => $response->status(),
                'body' => $response->json()
            ]);
        }

        return $response;
    }

    private function generateSalt($length = 12)
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        return substr(str_shuffle($characters), 0, $length);
    }
}

// Python'daki base64.urlsafe_b64encode eşdeğeri için yardımcı fonksiyon
function base64_url_encode($input)
{
    return strtr(base64_encode($input), '+/', '-_');
}