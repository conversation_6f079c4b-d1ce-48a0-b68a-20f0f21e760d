<?php
// Fallback definition for t() function if it doesn't exist
if (!function_exists('t')) {
    function t($key = null, $replace = [], $locale = null) {
        return \App\Helpers\TranslationHelper::trans($key, $replace, $locale);
    }
}
?>

@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 animate-fade-in">
                <h1>{{ t('hero_title') }}</h1>
                <p>{{ t('hero_subtitle') }}</p>
                <a href="#countries" class="btn btn-light btn-lg">{{ t('find_destination') }}</a>
            </div>
            <div class="col-lg-6 text-center animate-slide-up">
                <img src="{{ asset('images/logo.png') }}" alt="Vox eSIM" class="hero-image img-fluid">
            </div>
        </div>
    </div>
</section>

<!-- Search Section -->
<section class="search-section">
    <div class="container">
        <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="search-input" class="form-control" placeholder="{{ t('search_placeholder') }}">
            <div class="country-dropdown-container" style="display: none;">
                <div class="country-dropdown">
                    <div class="country-dropdown-header">
                        <h5>{{ t('select_country') }}</h5>
                    </div>
                    <div class="country-dropdown-body">
                        <div class="country-list">
                            @foreach ($locations as $location)
                                <div class="country-dropdown-item" data-country="{{ $location['name'] }}" data-code="{{ $location['code'] }}">
                                    <div class="country-flag-small">
                                        <img src="https://static.redteago.com/img/flags/{{ strtolower($location['code']) }}.png" alt="{{ $location['name'] }} flag" onerror="this.onerror=null; this.src=''; this.parentNode.innerHTML='<i class=\'fas fa-globe\'></i>';">
                                    </div>
                                    <span>{{ $location['name'] }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Best Sellers Section -->
<section class="best-sellers py-5" id="best-sellers">
    <div class="container">
        <!-- App Store Buttons -->
        <div class="app-stores-container text-center mb-4">
            <div class="app-stores-wrapper">
                <a href="#" class="app-store-btn apple-btn me-3" data-bs-toggle="modal" data-bs-target="#comingSoonModal">
                    <i class="fab fa-apple"></i> App Store
                </a>
                <a href="#" class="app-store-btn google-btn" data-bs-toggle="modal" data-bs-target="#comingSoonModal">
                    <i class="fab fa-google-play"></i> Google Play
                </a>
            </div>
        </div>
        
        <h2 class="section-title">{{ t('best_selling_plans') }}</h2>
        <div class="row">
            <!-- Best Seller 1 -->
            <div class="col-md-4 mb-4">
                <div class="package-card best-seller-card">
                    <div class="best-seller-badge">{{ t('popular') }}</div>
                    <div class="card-header text-center">
                        <h4>Europe Travel</h4>
                    </div>
                    <div class="card-body">
                        <div class="package-feature">
                            <i class="fas fa-globe"></i>
                            <span>Coverage in 30+ European countries</span>
                        </div>
                        <div class="package-feature">
                            <i class="fas fa-wifi"></i>
                            <span>3GB Data</span>
                        </div>
                        <div class="package-feature">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Valid for 30 days</span>
                        </div>
                        <div class="package-price">
                            $9.99 <small>USD</small>
                        </div>
                        <a href="/checkout/CKH006" class="btn btn-primary w-100">{{ t('buy_now') }}</a>
                    </div>
                </div>
            </div>

            <!-- Best Seller 2 -->
            <div class="col-md-4 mb-4">
                <div class="package-card best-seller-card">
                    <div class="best-seller-badge">{{ t('popular') }}</div>
                    <div class="card-header text-center">
                        <h4>Global Traveler</h4>
                    </div>
                    <div class="card-body">
                        <div class="package-feature">
                            <i class="fas fa-globe"></i>
                            <span>Coverage in 100+ countries</span>
                        </div>
                        <div class="package-feature">
                            <i class="fas fa-wifi"></i>
                            <span>6GB Data</span>
                        </div>
                        <div class="package-feature">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Valid for 15 days</span>
                        </div>
                        <div class="package-price">
                            $33.99 <small>USD</small>
                        </div>
                        <a href="/checkout/AIS002" class="btn btn-primary w-100">{{ t('buy_now') }}</a>
                    </div>
                </div>
            </div>

            <!-- Best Seller 3 -->
            <div class="col-md-4 mb-4">
                <div class="package-card best-seller-card">
                    <div class="best-seller-badge">{{ t('popular') }}</div>
                    <div class="card-header text-center">
                        <h4>USA Traveler</h4>
                    </div>
                    <div class="card-body">
                        <div class="package-feature">
                            <i class="fas fa-globe"></i>
                            <span>USA Coverage</span>
                        </div>
                        <div class="package-feature">
                            <i class="fas fa-wifi"></i>
                            <span>5GB Data</span>
                        </div>
                        <div class="package-feature">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Valid for 30 days</span>
                        </div>
                        <div class="package-price">
                            $4.99 <small>USD</small>
                        </div>
                        <a href="/checkout/PKJNMJIIU" class="btn btn-primary w-100">{{ t('buy_now') }}</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Countries Section -->
        <div id="countries" class="mt-5">
            <h2 class="section-title">{{ __('messages.supported_countries') }}</h2>

            @if(session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif

            <div class="row mt-4" id="countries-container">
                @foreach ($locations as $location)
                    <div class="col-sm-6 col-md-4 col-lg-3 mb-4 animate-fade-in country-item">
                        <a href="{{ route('country.show', ['code' => $location['code']]) }}" class="country-link">
                            <div class="country-card">
                                <div class="country-flag bg-light d-flex align-items-center justify-content-center">
                                    <img src="https://static.redteago.com/img/flags/{{ strtolower($location['code']) }}.png" alt="{{ $location['name'] }} flag" onerror="this.onerror=null; this.src=''; this.parentNode.innerHTML='<i class=\'fas fa-globe\'></i>';">
                                </div>
                                <h5 class="country-name">{{ $location['name'] }}</h5>
                                <span class="badge bg-primary">{{ __('messages.view_plans') }}</span>
                            </div>
                        </a>
                    </div>
                @endforeach
            </div>

            <div id="no-results" class="alert alert-info text-center mt-4" style="display: none;">
                <i class="fas fa-info-circle me-2"></i>
                {{ __('messages.no_countries_found') }}
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="how-it-works" id="how-it-works">
    <div class="container">
        <h2 class="section-title">{{ __('messages.how_it_works') }}</h2>
        <div class="row mt-5">
            <div class="col-md-4 mb-4">
                <div class="step-card position-relative">
                    <div class="step-number">1</div>
                    <div class="step-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h4 class="text-center mb-3">{{ __('messages.step_1_title') }}</h4>
                    <p class="text-center">{{ __('messages.step_1_desc') }}</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="step-card position-relative">
                    <div class="step-number">2</div>
                    <div class="step-icon">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <h4 class="text-center mb-3">{{ __('messages.step_2_title') }}</h4>
                    <p class="text-center">{{ __('messages.step_2_desc') }}</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="step-card position-relative">
                    <div class="step-number">3</div>
                    <div class="step-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <h4 class="text-center mb-3">{{ __('messages.step_3_title') }}</h4>
                    <p class="text-center">{{ __('messages.step_3_desc') }}</p>
                </div>
            </div>
        </div>
    </div>
</section>



<!-- Support Section -->
<section class="py-5 bg-light" id="support">
    <div class="container">
        <h2 class="section-title">{{ __('messages.need_help') }}</h2>
        <div class="row align-items-center mt-4">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <div class="bg-white p-4 rounded shadow-sm">
                    <h4 class="mb-3">{{ __('messages.contact_support') }}</h4>
                    <p>{{ __('messages.support_desc') }}</p>
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-envelope text-primary me-3"></i>
                        <span><EMAIL></span>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="bg-white p-4 rounded shadow-sm">
                    <h4 class="mb-3">{{ __('messages.faq') }}</h4>
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    {{ __('messages.what_is_esim') }}
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    {{ __('messages.what_is_esim_answer') }}
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    {{ __('messages.is_device_compatible') }}
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Most newer smartphones support eSIM technology, including iPhone XS and newer, Google Pixel 3 and newer, and many Samsung Galaxy models.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

