
@extends('layouts.app')

@section('content')
<!-- Checkout Header -->
<section class="py-4 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="javascript:history.back()">Package Selection</a></li>
                <li class="breadcrumb-item active">Checkout</li>
            </ol>
        </nav>
        <h1 class="mt-2">Secure Checkout</h1>
    </div>
</section>

<!-- Checkout Form -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <!-- Order Summary -->
            <div class="col-lg-4 order-lg-2 mb-4">
                <div class="checkout-container">
                    <h4 class="mb-3">Order Summary</h4>
                    <div class="package-details">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">{{ $package['name'] }}</span>
                            <span>${{ $package['price'] }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Data</span>
                            <span>{{ $package['data'] }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span>Validity</span>
                            <span>{{ $package['validity'] }} days</span>
                        </div>
                        <hr>
                        <div id="rapyd-discount" style="display: none;">
                            <div class="d-flex justify-content-between text-success">
                                <span>Rapyd Discount (2%)</span>
                                <span>-${{ number_format($package['price'] * 0.02, 2) }}</span>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>Total</span>
                            <span id="total-price">${{ $package['price'] }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Checkout Form -->
            <div class="col-lg-8 order-lg-1">
                <div class="checkout-container">
                    <h4 class="mb-4">Customer Information</h4>
                    <form action="{{ route('esim.purchase') }}" method="POST" id="checkout-form">
                        @csrf
                        <input type="hidden" name="package_code" value="{{ $package['packageCode'] }}">

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label">Full Name</label>
                                <input type="text" name="name" class="form-control" value="{{ Auth::check() ? Auth::user()->name : '' }}" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Email Address</label>
                            <input type="email" name="email" class="form-control" value="{{ Auth::check() ? Auth::user()->email : '' }}" required>
                            <small class="form-text text-muted">We'll send your eSIM details to this email address.</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Phone Number (optional)</label>
                            <input type="tel" name="phone" class="form-control">
                        </div>

                        <h4 class="mt-5 mb-4">Payment Method</h4>

                        <div class="payment-method-selector mb-4">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="payment-option card h-100">
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="payment_method" id="payment-card" value="card" checked>
                                                <label class="form-check-label w-100" for="payment-card">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fab fa-stripe fa-2x text-primary me-3"></i>
                                                        <div>
                                                            <span class="d-block fw-bold">Credit Card (Stripe)</span>
                                                            <small class="text-muted">Visa, Mastercard, Amex</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Rapyd payment option commented out temporarily
                                <div class="col-md-6 mb-3">
                                    <div class="payment-option card h-100">
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="payment_method" id="payment-rapyd" value="rapyd">
                                                <label class="form-check-label w-100" for="payment-rapyd">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-wallet fa-2x text-primary me-3"></i>
                                                        <div>
                                                            <span class="d-block fw-bold">Rapyd Pay</span>
                                                            <small class="text-muted">Multiple payment methods</small>
                                                            <span class="badge bg-success ms-2">2% discount!</span>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                -->
                            </div>
                        </div>

                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="terms-check" required>
                            <label class="form-check-label" for="terms-check">
                                I agree to the <a href="{{ route('terms') }}" target="_blank">Terms of Service</a> and <a href="{{ route('privacy') }}" target="_blank">Privacy Policy</a>
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg w-100">Proceed to Payment</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Trust Badges -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fas fa-lock fa-2x text-primary me-2"></i>
                    <span>Secure Payment</span>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fas fa-bolt fa-2x text-primary me-2"></i>
                    <span>Instant Delivery</span>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fas fa-headset fa-2x text-primary me-2"></i>
                    <span>24/7 Support</span>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fas fa-shield-alt fa-2x text-primary me-2"></i>
                    <span>Secure Connection</span>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const rapydRadio = document.getElementById('payment-rapyd');
    const stripeRadio = document.getElementById('payment-card');
    const rapydDiscount = document.getElementById('rapyd-discount');
    const totalPrice = document.getElementById('total-price');
    const originalPrice = {{ $package['price'] }};

    function updatePrice() {
        if (rapydRadio.checked) {
            rapydDiscount.style.display = 'block';
            const discountedPrice = originalPrice * 0.98;
            totalPrice.textContent = '$' + discountedPrice.toFixed(2);
        } else {
            rapydDiscount.style.display = 'none';
            totalPrice.textContent = '$' + originalPrice.toFixed(2);
        }
    }

    rapydRadio.addEventListener('change', updatePrice);
    stripeRadio.addEventListener('change', updatePrice);
});
</script>
@endpush





