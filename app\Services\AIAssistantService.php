<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AIAssistantService
{
    protected $apiKey;
    protected $baseUrl;
    protected $assistantId;

    public function __construct()
    {
        $this->apiKey = env('OPENAI_API_KEY');
        $this->baseUrl = 'https://api.openai.com/v1';
        $this->assistantId = env('OPENAI_ASSISTANT_ID');
        
        if (!$this->apiKey) {
            Log::error('OpenAI API key is missing');
        }
        
        if (!$this->assistantId) {
            Log::error('OpenAI Assistant ID is missing');
        }
    }

    /**
     * Create a new thread
     *
     * @return string|null Thread ID
     */
    public function createThread()
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'OpenAI-Beta' => 'assistants=v2'  // v1'den v2'ye güncellendi
            ])->post($this->baseUrl . '/threads');

            if ($response->successful()) {
                return $response->json('id');
            }

            Log::error('Failed to create thread', [
                'status' => $response->status(),
                'response' => $response->json()
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception when creating thread', [
                'message' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Send a message to a thread
     *
     * @param string $threadId
     * @param string $message
     * @return array|null
     */
    public function sendMessage($threadId, $message)
    {
        try {
            // Add message to thread
            $messageResponse = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'OpenAI-Beta' => 'assistants=v2'  // v1'den v2'ye güncellendi
            ])->post($this->baseUrl . '/threads/' . $threadId . '/messages', [
                'role' => 'user',
                'content' => $message
            ]);

            if (!$messageResponse->successful()) {
                Log::error('Failed to add message to thread', [
                    'status' => $messageResponse->status(),
                    'response' => $messageResponse->json()
                ]);
                return null;
            }

            // Run the assistant
            $runResponse = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'OpenAI-Beta' => 'assistants=v2'  // v1'den v2'ye güncellendi
            ])->post($this->baseUrl . '/threads/' . $threadId . '/runs', [
                'assistant_id' => $this->assistantId
            ]);

            if ($runResponse->successful()) {
                return [
                    'thread_id' => $threadId,
                    'run_id' => $runResponse->json('id')
                ];
            }

            Log::error('Failed to run assistant', [
                'status' => $runResponse->status(),
                'response' => $runResponse->json()
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception when sending message', [
                'message' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Check the status of a run
     *
     * @param string $threadId
     * @param string $runId
     * @return array|null
     */
    public function checkRunStatus($threadId, $runId)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'OpenAI-Beta' => 'assistants=v2'  // v1'den v2'ye güncellendi
            ])->get($this->baseUrl . '/threads/' . $threadId . '/runs/' . $runId);

            if (!$response->successful()) {
                Log::error('Failed to check run status', [
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);
                return null;
            }

            $status = $response->json('status');
            $isComplete = in_array($status, ['completed', 'failed', 'cancelled', 'expired']);

            if ($isComplete && $status === 'completed') {
                // Get the assistant's response
                $messagesResponse = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                    'OpenAI-Beta' => 'assistants=v2'  // v1'den v2'ye güncellendi
                ])->get($this->baseUrl . '/threads/' . $threadId . '/messages', [
                    'limit' => 1,
                    'order' => 'desc'
                ]);

                if ($messagesResponse->successful()) {
                    $messages = $messagesResponse->json('data');
                    if (!empty($messages) && $messages[0]['role'] === 'assistant') {
                        $content = $messages[0]['content'][0]['text']['value'] ?? '';
                        return [
                            'is_complete' => true,
                            'message' => $content
                        ];
                    }
                }

                Log::error('Failed to get assistant response', [
                    'status' => $messagesResponse->status(),
                    'response' => $messagesResponse->json()
                ]);
                return [
                    'is_complete' => true,
                    'message' => 'Üzgünüm, yanıt alınamadı.'
                ];
            }

            return [
                'is_complete' => $isComplete,
                'message' => null
            ];
        } catch (\Exception $e) {
            Log::error('Exception when checking run status', [
                'message' => $e->getMessage()
            ]);
            return null;
        }
    }
}

