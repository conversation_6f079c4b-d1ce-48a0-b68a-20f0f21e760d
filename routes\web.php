<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\EsimController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\AuthController;

Route::get('/', [EsimController::class, 'index'])->name('home');
Route::get('/packages/{countryCode}', [EsimController::class, 'showPackages'])->name('packages.show');
Route::get('/country/{code}', [EsimController::class, 'showCountry'])
    ->name('country.show');
Route::get('/country/{code}/filter', [EsimController::class, 'filterPackages'])
    ->name('country.filter');
Route::get('/country/{code}/packages', [EsimController::class, 'showCountry'])->name('country.packages');
Route::get('/checkout/{packageCode}', [EsimController::class, 'checkout'])->name('esim.checkout');
Route::post('/purchase', [EsimController::class, 'purchase'])->name('esim.purchase');
Route::post('/topup/purchase', [EsimController::class, 'purchaseTopup'])->name('topup.purchase');

Route::post('/payment/callback', [App\Http\Controllers\EsimController::class, 'paymentCallback'])->name('payment.callback');
Route::get('/payment/callback', [App\Http\Controllers\EsimController::class, 'paymentCallback'])->name('payment.callback.get');

Route::get('/payment/success', [EsimController::class, 'paymentSuccess'])->name('payment.success');
Route::get('/payment/cancel', [EsimController::class, 'paymentCancel'])->name('payment.cancel');

Route::get('/esim/usage/{esimTranNo}', [EsimController::class, 'checkUsage'])
    ->name('esim.usage');

Route::get('/esim/package-status/{orderNo}', [EsimController::class, 'getPackageStatus'])
    ->middleware(['auth'])
    ->name('esim.package-status');

// Dil değiştirme route'u
Route::get('/language/{lang}', [LanguageController::class, 'switchLang'])->name('language.switch');

// Auth routes
Route::get('/login', [App\Http\Controllers\AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [App\Http\Controllers\AuthController::class, 'sendLoginLink'])->name('login.send');
Route::get('/login/token/{token}', [App\Http\Controllers\AuthController::class, 'loginWithToken'])->name('login.token');
Route::post('/logout', [App\Http\Controllers\AuthController::class, 'logout'])->name('logout');

// Profile routes - Auth middleware ile korumalı
Route::middleware(['auth'])->group(function () {
    Route::get('/profile/dashboard', [App\Http\Controllers\AuthController::class, 'dashboard'])->name('profile.dashboard');
    Route::post('/profile/query-esim', [App\Http\Controllers\AuthController::class, 'queryEsim'])->name('profile.query-esim');
});

// Legal pages
Route::get('/terms', function () {
    return view('legal.terms');
})->name('terms');

Route::get('/privacy', function () {
    return view('legal.privacy');
})->name('privacy');

Route::get('/cookie-policy', function () {
    return view('legal.cookie-policy');
})->name('cookie.policy');

// Resend eSIM email
Route::post('/profile/resend-esim-email', [App\Http\Controllers\EsimController::class, 'resendEsimEmail'])->name('profile.resend-esim-email')->middleware('auth');

// AI Assistant Routes
Route::post('/ai/create-thread', [App\Http\Controllers\AIAssistantController::class, 'createThread']);
Route::post('/ai/send-message', [App\Http\Controllers\AIAssistantController::class, 'sendMessage']);
Route::post('/ai/check-run-status', [App\Http\Controllers\AIAssistantController::class, 'checkRunStatus']);







