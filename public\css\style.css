/* Vox eSIM - Corporate CSS Styles
   Version: 3.0
*/

:root {
  /* Main Corporate Colors */
  --primary-color: #2c3e50;    /* Dark blue-gray */
  --secondary-color: #3498db;  /* Bright blue */
  --accent-color: #e74c3c;     /* Accent red */
  --background-color: #f5f5f5; /* Light gray background */
  --card-bg-color: #ffffff;    /* White */
  --text-color: #333333;       /* Dark gray text */
  --text-light: #6c757d;       /* Medium gray text */
  --border-color: #dee2e6;     /* Light border */
  --success-color: #27ae60;    /* Green */
  --warning-color: #f39c12;    /* Orange */
  --danger-color: #c0392b;     /* Red */
  --info-color: #2980b9;       /* Blue */

  /* Simplified Shadows */
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --hover-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);

  /* Transitions */
  --transition-speed: 0.2s;
}

/* Base Styles */
body {
  font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5;
  overflow-x: hidden;
  font-size: 16px;
}

a {
  color: var(--secondary-color);
  text-decoration: none;
  transition: color var(--transition-speed) ease;
}

a:hover {
  color: var(--primary-color);
}

/* Button Styles - Simplified */
.btn {
  transition: background-color var(--transition-speed) ease;
  border-radius: 4px;
  font-weight: 500;
  padding: 0.5rem 1.25rem;
  border: none;
}

.btn-primary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-color);
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
}

.btn-outline-primary:hover {
  background-color: var(--secondary-color);
  color: white;
}

/* Header Styles - Simplified */
.navbar {
  background-color: white !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  padding: 0.7rem 1rem;
}

.navbar-brand {
  font-weight: 600;
  font-size: 1.4rem;
  color: var(--primary-color) !important;
}

.navbar-brand img {
  height: 36px;
  margin-right: 8px;
}

.nav-link {
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  color: var(--text-color) !important;
  position: relative;
  transition: color var(--transition-speed) ease;
}

.nav-link:hover {
  color: var(--secondary-color) !important;
}

/* Simplified navigation indicator */
.nav-link.active {
  color: var(--secondary-color) !important;
  font-weight: 600;
}

.language-selector {
  margin-left: 10px;
}

.language-selector .dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
}

.language-selector .dropdown-item img {
  width: 18px;
  margin-right: 8px;
}

/* Hero Section - Simplified */
.hero-section {
  background-color: var(--primary-color);
  color: white;
  padding: 4rem 0;
  margin-bottom: 2rem;
}

.hero-section h1 {
  font-weight: 600;
  font-size: 2.2rem;
  margin-bottom: 1.2rem;
  line-height: 1.3;
}

.hero-section p {
  font-size: 1rem;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.hero-image {
  max-width: 100%;
  /* Removed animation for cleaner look */
}

/* Search Section - Simplified */
.search-section {
  padding: 1.5rem 0;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.search-box {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.search-box input {
  padding-left: 2.5rem;
  height: 44px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  transition: border-color var(--transition-speed) ease;
}

.search-box input:focus {
  border-color: var(--secondary-color);
  box-shadow: none;
  outline: none;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
}

/* Country Dropdown - Simplified */
.country-dropdown-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 4px;
}

.country-dropdown {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-height: 350px;
  border: 1px solid var(--border-color);
}

.country-dropdown-header {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-color);
  background-color: #f8f9fa;
}

.country-dropdown-header h5 {
  margin: 0;
  font-size: 15px;
  color: var(--text-color);
  font-weight: 500;
}

.country-dropdown-body {
  max-height: 300px;
  overflow-y: auto;
}

.country-list {
  padding: 5px 0;
}

.country-dropdown-item {
  display: flex;
  align-items: center;
  padding: 8px 15px;
  cursor: pointer;
  transition: background-color var(--transition-speed) ease;
}

.country-dropdown-item:hover {
  background-color: #f5f5f5;
}

.country-flag-small {
  width: 24px;
  height: 24px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
  overflow: hidden;
}

.country-flag-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.country-flag-small i {
  color: var(--secondary-color);
  font-size: 14px;
}

/* Country Cards - Simplified */
.country-section {
  padding: 2rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 2rem;
  font-weight: 600;
  color: var(--primary-color);
  font-size: 1.8rem;
}

/* Removed decorative underline for cleaner look */

.country-card {
  background-color: var(--card-bg-color);
  border-radius: 4px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: box-shadow var(--transition-speed) ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.2rem;
  text-align: center;
  border: 1px solid var(--border-color);
}

.country-card:hover {
  box-shadow: var(--hover-shadow);
  border-color: var(--secondary-color);
}

.highlight-country {
  border: 1px solid var(--secondary-color);
}

.country-flag {
  width: 48px;
  height: 48px;
  border-radius: 2px;
  object-fit: cover;
  margin-bottom: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #f8f9fa;
}

.country-flag img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.country-name {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.country-link {
  text-decoration: none;
  color: inherit;
}

/* Package Cards - Simplified */
.package-card {
  background-color: var(--card-bg-color);
  border-radius: 4px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: box-shadow var(--transition-speed) ease;
  height: 100%;
  border: 1px solid var(--border-color);
  position: relative;
}

.package-card:hover {
  box-shadow: var(--hover-shadow);
  border-color: var(--secondary-color);
}

.package-card .card-header {
  background-color: var(--secondary-color);
  color: white;
  font-weight: 500;
  padding: 0.8rem;
  text-align: center;
}

.package-card .card-body {
  padding: 1.2rem;
}

/* Coverage Feature Styles */
.coverage-feature {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  background-color: rgba(240, 249, 255, 0.7);
  border-radius: 8px;
  padding: 10px 12px;
  border: 1px solid rgba(0, 149, 255, 0.1);
}

.coverage-feature i.fas.fa-globe {
  color: #0095ff;
  font-size: 16px;
  margin-right: 12px;
}

.coverage-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}

.coverage-info span {
  font-weight: 500;
  color: #333;
}

.coverage-info-btn {
  background-color: #0095ff;
  color: white;
  border: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 149, 255, 0.2);
  margin-left: 10px;
}

.coverage-info-btn:hover {
  background-color: #007acc;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 149, 255, 0.3);
}

.coverage-info-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 149, 255, 0.2);
}

.coverage-info-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 149, 255, 0.3);
}

/* Diğer paket özellikleri için stil */
.package-feature {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 0;
}

.package-feature i {
  color: var(--secondary-color);
  font-size: 16px;
  width: 24px;
  margin-right: 12px;
  text-align: center;
}

.coverage-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}

.coverage-container span {
  font-weight: 500;
  color: var(--text-color);
}

.coverage-details-link {
  font-size: 13px;
  color: #0066cc;
  text-decoration: none;
  transition: all 0.2s ease;
  font-weight: 500;
  margin-left: 12px;
  white-space: nowrap;
  padding: 4px 10px;
  border-radius: 4px;
  background-color: rgba(0, 102, 204, 0.08);
  border: 1px solid rgba(0, 102, 204, 0.15);
  display: inline-flex;
  align-items: center;
}

.coverage-details-link:hover {
  color: #0052a3;
  background-color: rgba(0, 102, 204, 0.12);
  border-color: rgba(0, 102, 204, 0.25);
  text-decoration: none;
}

.coverage-details-link:active {
  background-color: rgba(0, 102, 204, 0.18);
}

.coverage-details-link:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.25);
}

.package-feature span {
  flex: 1;
}

.package-price {
  font-size: 1.6rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 1rem 0;
  text-align: center;
}

.package-price small {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 400;
}

/* Best Sellers Section - Simplified */
.best-sellers {
  padding: 2rem 0;
  background-color: #f5f5f5;
}

.best-seller-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: var(--accent-color);
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 2px;
  font-size: 0.75rem;
  font-weight: 500;
  z-index: 1;
}

/* App Store Buttons in Best Sellers Section */
.app-stores-container {
  margin-bottom: 30px;
}

.app-stores-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 15px;
}

.best-sellers .app-store-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.25s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  min-width: 140px;
}

.best-sellers .app-store-btn i {
  font-size: 20px;
  margin-right: 10px;
}

/* Apple Store Button */
.best-sellers .apple-btn {
  background-color: #000000;
  color: white;
  border: 1px solid #000000;
}

.best-sellers .apple-btn:hover {
  background-color: #333333;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Google Play Button */
.best-sellers .google-btn {
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #dddddd;
}

.best-sellers .google-btn:hover {
  background-color: #f8f9fa;
  color: #333333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.best-sellers .app-store-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* How It Works Section - Simplified */
.how-it-works {
  padding: 3rem 0;
  background-color: white;
}

.step-icon {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  background-color: var(--secondary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.2rem;
  font-size: 1.5rem;
}

.step-card {
  padding: 1.5rem;
  border-radius: 4px;
  background-color: white;
  box-shadow: var(--card-shadow);
  height: 100%;
  border: 1px solid var(--border-color);
}

.step-card:hover {
  border-color: var(--secondary-color);
}

.step-number {
  font-size: 2.5rem;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.08);
  position: absolute;
  top: 10px;
  right: 15px;
}

/* Footer - Simplified */
.footer {
  background-color: var(--primary-color);
  color: rgba(255, 255, 255, 0.7);
  padding: 2.5rem 0 1rem;
}

.footer-logo {
  margin-bottom: 1.2rem;
}

.footer-logo img {
  height: 36px;
}

.footer h5 {
  color: white;
  font-weight: 500;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 0.6rem;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  transition: color var(--transition-speed) ease;
}

.footer-links a:hover {
  color: white;
  text-decoration: none;
}

.social-links {
  display: flex;
  gap: 12px;
}

.social-links a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  transition: background-color var(--transition-speed) ease;
}

.social-links a:hover {
  background-color: var(--secondary-color);
}

/* Login Button Styles - Simplified */
.nav-item .login-btn {
  background-color: var(--secondary-color);
  color: white !important;
  border-radius: 4px;
  padding: 0.4rem 1.2rem !important;
  margin-left: 8px;
  transition: background-color var(--transition-speed) ease;
}

.nav-item .login-btn:hover {
  background-color: var(--primary-color);
}

.nav-item .login-btn::after {
  display: none;
}

/* User Dropdown Styles - Simplified */
.user-dropdown .nav-link {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 0.4rem 1rem !important;
  transition: background-color var(--transition-speed) ease;
}

.user-dropdown .nav-link:hover {
  background-color: #e9ecef;
}

.user-dropdown .dropdown-menu {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  padding: 0.4rem 0;
  margin-top: 0.4rem;
}

.user-dropdown .dropdown-item {
  padding: 0.5rem 1rem;
  transition: background-color var(--transition-speed) ease;
}

.user-dropdown .dropdown-item:hover {
  background-color: #f5f5f5;
  color: var(--secondary-color);
}

.user-dropdown .dropdown-item:active {
  background-color: var(--secondary-color);
  color: white;
}

/* Bottom Bar - Simplified */
.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1.2rem;
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Fix for script tags in views */
script {
  display: none !important;
}

/* Checkout Page - Simplified */
.checkout-container {
  background-color: white;
  border-radius: 4px;
  box-shadow: var(--card-shadow);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
}

.checkout-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  position: relative;
}

.checkout-steps::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--border-color);
  z-index: 1;
}

.step {
  position: relative;
  z-index: 2;
  background-color: white;
  padding: 0 8px;
  text-align: center;
}

.step-number {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  background-color: var(--border-color);
  color: var(--text-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;
  font-weight: 500;
}

.step.active .step-number {
  background-color: var(--secondary-color);
  color: white;
}

.step-label {
  font-size: 0.85rem;
  color: var(--text-light);
}

.step.active .step-label {
  color: var(--secondary-color);
  font-weight: 500;
}

.payment-option {
  cursor: pointer;
  transition: border-color var(--transition-speed) ease;
  border: 1px solid var(--border-color);
}

.payment-option:hover {
  border-color: var(--secondary-color);
}

.payment-option .form-check-input:checked ~ .form-check-label {
  color: var(--secondary-color);
}

.payment-option .form-check-input:checked ~ .form-check-label i {
  color: var(--secondary-color);
}

.payment-option .form-check-input:checked + .form-check-label .payment-option {
  border-color: var(--secondary-color);
}

/* Success Page - Simplified */
.success-checkmark {
  width: 70px;
  height: 70px;
  margin: 0 auto;
  position: relative;
}

.check-icon {
  width: 70px;
  height: 70px;
  position: relative;
  border-radius: 4px;
  box-sizing: content-box;
  border: 3px solid var(--success-color);
}

.check-icon::before {
  top: 3px;
  left: -2px;
  width: 25px;
  transform-origin: 100% 50%;
  border-radius: 100px 0 0 100px;
}

.check-icon::after {
  top: 0;
  left: 25px;
  width: 50px;
  transform-origin: 0 50%;
  border-radius: 0 100px 100px 0;
  animation: rotate-circle 2s ease-in;
}

.check-icon::before, .check-icon::after {
  content: '';
  height: 80px;
  position: absolute;
  background: #FFFFFF;
  transform: rotate(-45deg);
}

.check-icon .icon-line {
  height: 4px;
  background-color: var(--success-color);
  display: block;
  border-radius: 2px;
  position: absolute;
  z-index: 10;
}

.check-icon .icon-line.line-tip {
  top: 40px;
  left: 12px;
  width: 20px;
  transform: rotate(45deg);
  animation: icon-line-tip 0.5s;
}

.check-icon .icon-line.line-long {
  top: 32px;
  right: 8px;
  width: 40px;
  transform: rotate(-45deg);
  animation: icon-line-long 0.5s;
}

.check-icon .icon-circle {
  top: -3px;
  left: -3px;
  z-index: 10;
  width: 70px;
  height: 70px;
  border-radius: 4px;
  position: absolute;
  box-sizing: content-box;
  border: 3px solid rgba(39, 174, 96, 0.5);
}

.check-icon .icon-fix {
  top: 8px;
  width: 4px;
  left: 22px;
  z-index: 1;
  height: 70px;
  position: absolute;
  transform: rotate(-45deg);
  background-color: #FFFFFF;
}

@keyframes rotate-circle {
  0% { transform: rotate(-45deg); }
  5% { transform: rotate(-45deg); }
  12% { transform: rotate(-405deg); }
  100% { transform: rotate(-405deg); }
}

@keyframes icon-line-tip {
  0% { width: 0; left: 1px; top: 19px; }
  54% { width: 0; left: 1px; top: 19px; }
  70% { width: 30px; left: -4px; top: 32px; }
  84% { width: 15px; left: 18px; top: 42px; }
  100% { width: 20px; left: 12px; top: 40px; }
}

@keyframes icon-line-long {
  0% { width: 0; right: 40px; top: 54px; }
  65% { width: 0; right: 40px; top: 54px; }
  84% { width: 45px; right: 0px; top: 30px; }
  100% { width: 40px; right: 8px; top: 32px; }
}

/* Language Switcher - Simplified */
.language-switcher {
  display: flex;
  align-items: center;
}

.language-switcher .dropdown-toggle {
  display: flex;
  align-items: center;
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.9rem;
}

.language-switcher .dropdown-toggle img {
  width: 18px;
  margin-right: 4px;
}

.language-switcher .dropdown-menu {
  min-width: 180px;
  padding: 0.4rem 0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  max-height: 250px;
  overflow-y: auto;
}

.language-switcher .dropdown-menu::-webkit-scrollbar {
  width: 4px;
}

.language-switcher .dropdown-menu::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.language-switcher .dropdown-menu::-webkit-scrollbar-thumb {
  background: var(--secondary-color);
  border-radius: 2px;
}

.language-switcher .dropdown-item {
  padding: 0.4rem 0.8rem;
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.language-switcher .dropdown-item img {
  width: 18px;
  margin-right: 8px;
}

.language-switcher .dropdown-item:hover {
  background-color: #f5f5f5;
}

/* Animations - Simplified */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  animation: slideUp 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive Styles - Simplified */
@media (max-width: 992px) {
  .hero-section {
    text-align: center;
    padding: 2.5rem 0;
  }

  .hero-image {
    margin-top: 1.5rem;
    max-width: 80%;
  }

  .navbar-collapse {
    background-color: white;
    padding: 0.8rem;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .checkout-steps {
    overflow-x: auto;
    padding-bottom: 0.8rem;
  }

  .section-title {
    font-size: 1.6rem;
  }
}

@media (max-width: 768px) {
  .checkout-steps {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.8rem;
  }

  .checkout-steps::before {
    display: none;
  }

  .step {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .step-number {
    margin: 0 12px 0 0;
  }

  .hero-section h1 {
    font-size: 1.8rem;
  }

  .footer {
    text-align: center;
  }

  .social-links {
    justify-content: center;
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 576px) {
  .hero-section {
    padding: 1.5rem 0;
  }

  .hero-section h1 {
    font-size: 1.6rem;
  }

  .checkout-container {
    padding: 1.2rem;
  }

  .package-card .card-body {
    padding: 0.8rem;
  }

  .country-card {
    padding: 1rem;
  }
}

/* App Store Buttons */
.app-stores {
  display: flex;
  align-items: center;
  margin-top: 15px;
}

.app-store-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.25s ease;
}

.app-store-btn i {
  font-size: 18px;
  margin-right: 8px;
}

/* Apple Store Button */
.apple-btn {
  background-color: #000000;
  color: white;
  border: 1px solid #000000;
}

.apple-btn:hover {
  background-color: #333333;
  color: white;
}

/* Google Play Button */
.google-btn {
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #dddddd;
}

.google-btn:hover {
  background-color: #f8f9fa;
  color: #333333;
}

.footer .app-store-btn {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.footer .app-store-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
}

.footer .app-store-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Coming Soon Modal */
.coming-soon-icon {
  background-color: rgba(44, 62, 80, 0.05);
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  padding: 15px;
}

.coming-soon-icon img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.modal-body h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-weight: 600;
}

.modal-body p {
  color: #555;
  font-size: 16px;
  line-height: 1.5;
}



