/*
===========================================
VOX eSIM - MODERN CORPORATE DESIGN SYSTEM
Version: 5.0 - Complete Redesign
===========================================
*/

/* Import Ultra Modern Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Advanced CSS Features */
@supports (backdrop-filter: blur(10px)) {
  .glass-effect {
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
  }
}

@supports (background-clip: text) {
  .gradient-text {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

:root {
  /* Modern Corporate Color Palette */
  --primary-dark: #0f172a;      /* Deep slate - Primary brand color */
  --primary-blue: #1e40af;      /* Professional blue - Secondary brand */
  --accent-orange: #f59e0b;     /* Vibrant orange - Accent color */
  --neutral-light: #f8fafc;     /* Light background - Neutral */

  /* Extended Color System */
  --white: #ffffff;
  --black: #000000;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e0;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Semantic Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Modern Typography Scale */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-display: 'Poppins', sans-serif;

  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* Modern Spacing Scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */

  /* Modern Border Radius */
  --radius-none: 0;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Modern Shadow System */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Modern Transitions */
  --transition-all: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-colors: color 0.15s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-transform: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-opacity: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);

  /* Ultra Modern Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-blue) 100%);
  --gradient-accent: linear-gradient(135deg, var(--primary-blue) 0%, var(--accent-orange) 100%);
  --gradient-light: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  --gradient-rainbow: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  --gradient-sunset: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --gradient-ocean: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-fire: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);

  /* Advanced Shadows with Color */
  --shadow-primary: 0 10px 25px -5px rgba(30, 64, 175, 0.25), 0 8px 10px -6px rgba(30, 64, 175, 0.1);
  --shadow-accent: 0 10px 25px -5px rgba(245, 158, 11, 0.25), 0 8px 10px -6px rgba(245, 158, 11, 0.1);
  --shadow-success: 0 10px 25px -5px rgba(16, 185, 129, 0.25), 0 8px 10px -6px rgba(16, 185, 129, 0.1);

  /* Glassmorphism Effects */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-dark-bg: rgba(15, 23, 42, 0.25);
  --glass-dark-border: rgba(15, 23, 42, 0.18);

  /* Advanced Animations */
  --bounce-in: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);

  /* Neon Effects */
  --neon-blue: 0 0 5px var(--primary-blue), 0 0 10px var(--primary-blue), 0 0 15px var(--primary-blue);
  --neon-orange: 0 0 5px var(--accent-orange), 0 0 10px var(--accent-orange), 0 0 15px var(--accent-orange);
}

/*
===========================================
MODERN RESET & BASE STYLES
===========================================
*/

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  font-weight: var(--font-normal);
  font-size: 1rem;
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Modern Typography System */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-display);
  font-weight: var(--font-bold);
  line-height: 1.2;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
  letter-spacing: -0.025em;
}

h1 { font-size: 3.75rem; font-weight: var(--font-extrabold); }
h2 { font-size: 3rem; font-weight: var(--font-bold); }
h3 { font-size: 2.25rem; font-weight: var(--font-bold); }
h4 { font-size: 1.875rem; font-weight: var(--font-semibold); }
h5 { font-size: 1.5rem; font-weight: var(--font-semibold); }
h6 { font-size: 1.25rem; font-weight: var(--font-medium); }

p {
  margin-bottom: var(--space-4);
  color: var(--gray-600);
  line-height: 1.7;
}

/* Modern Link Styles */
a {
  color: var(--primary-blue);
  text-decoration: none;
  font-weight: var(--font-medium);
  transition: var(--transition-colors);
}

a:hover {
  color: var(--primary-dark);
}

a:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Modern List Styles */
ul, ol {
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
}

li {
  margin-bottom: var(--space-2);
  color: var(--gray-600);
}

/* Modern Image Styles */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Modern Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-family-primary);
  font-size: 0.875rem;
  font-weight: var(--font-semibold);
  line-height: 1;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-all);
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.3);
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--white);
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--white);
}

.btn-secondary {
  background: var(--white);
  color: var(--primary-blue);
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--primary-blue);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-accent {
  background: var(--gradient-accent);
  color: var(--white);
  border-color: var(--accent-orange);
  box-shadow: var(--shadow-sm);
}

.btn-accent:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--white);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: 1rem;
  border-radius: var(--radius-xl);
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: 0.75rem;
  border-radius: var(--radius-md);
}

/* Modern Card System */
.card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: var(--transition-all);
}

.card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.card-header {
  padding: var(--space-6);
  background: var(--gradient-light);
  border-bottom: 1px solid var(--gray-200);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
}

/*
===========================================
MODERN NAVIGATION SYSTEM
===========================================
*/

.navbar {
  background: var(--glass-bg) !important;
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid var(--glass-border);
  box-shadow: var(--shadow-lg);
  padding: var(--space-4) 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s var(--ease-out-expo);
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98) !important;
  box-shadow: var(--shadow-xl);
  padding: var(--space-3) 0;
}

.navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-accent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.navbar.scrolled::before {
  opacity: 1;
}

.navbar-brand {
  font-family: var(--font-family-display);
  font-weight: var(--font-extrabold);
  font-size: 1.75rem;
  color: var(--primary-dark) !important;
  letter-spacing: -0.05em;
  transition: var(--transition-transform);
}

.navbar-brand:hover {
  color: var(--primary-blue) !important;
  transform: scale(1.05);
}

.navbar-brand img {
  height: 48px;
  margin-right: var(--space-3);
  transition: var(--transition-transform);
}

.navbar-brand:hover img {
  transform: rotate(5deg);
}

.navbar-nav .nav-link {
  font-family: var(--font-family-primary);
  font-weight: var(--font-medium);
  font-size: 0.9rem;
  color: var(--gray-700) !important;
  padding: var(--space-3) var(--space-4) !important;
  margin: 0 var(--space-1);
  border-radius: var(--radius-lg);
  transition: var(--transition-all);
  position: relative;
}

.navbar-nav .nav-link:hover {
  color: var(--primary-blue) !important;
  background-color: rgba(30, 64, 175, 0.1);
  transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
  color: var(--primary-blue) !important;
  background-color: rgba(30, 64, 175, 0.15);
  font-weight: var(--font-semibold);
}

.navbar-nav .nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-accent);
  transition: var(--transition-all);
  transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
  width: 80%;
}

/* Modern Login Button */
.nav-item .login-btn {
  background: var(--gradient-primary);
  color: var(--white) !important;
  border-radius: var(--radius-xl);
  padding: var(--space-3) var(--space-6) !important;
  margin-left: var(--space-4);
  font-weight: var(--font-semibold);
  border: 2px solid transparent;
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
}

.nav-item .login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.nav-item .login-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-orange);
}

.nav-item .login-btn:hover::before {
  left: 100%;
}

/* Mobile Navigation */
.navbar-toggler {
  border: none;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: var(--transition-all);
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.3);
}

.navbar-collapse {
  background: var(--white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  margin-top: var(--space-4);
  padding: var(--space-6);
}

/*
===========================================
MODERN HERO SECTION
===========================================
*/

.hero-section {
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--space-24) 0;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

/* Parallax Background Layers */
.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(245, 158, 11, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(30, 64, 175, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
  animation: heroParallax 20s ease-in-out infinite;
}

.hero-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="heroGrid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23heroGrid)"/></svg>');
  opacity: 0.4;
  animation: heroGridMove 30s linear infinite;
}

@keyframes heroParallax {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 1;
  }
  25% {
    transform: translateY(-10px) rotate(1deg) scale(1.02);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-20px) rotate(0deg) scale(1.05);
    opacity: 0.9;
  }
  75% {
    transform: translateY(-10px) rotate(-1deg) scale(1.02);
    opacity: 0.8;
  }
}

@keyframes heroGridMove {
  0% { transform: translateX(0) translateY(0); }
  100% { transform: translateX(10px) translateY(10px); }
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(30, 64, 175, 0.1) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

.hero-section .container {
  position: relative;
  z-index: 2;
}

.hero-section h1 {
  font-size: 4.5rem;
  font-weight: var(--font-extrabold);
  line-height: 1.1;
  margin-bottom: var(--space-6);
  color: var(--white);
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.hero-section p {
  font-size: 1.25rem;
  font-weight: var(--font-normal);
  line-height: 1.6;
  margin-bottom: var(--space-8);
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
}

.hero-image {
  max-width: 100%;
  filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.3));
  transition: var(--transition-transform);
  animation: heroFloat 6s ease-in-out infinite;
}

@keyframes heroFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-10px) scale(1.02); }
}

.hero-image:hover {
  transform: scale(1.05) rotate(2deg);
}

/* Modern CTA Buttons */
.hero-cta {
  display: flex;
  gap: var(--space-4);
  align-items: center;
  flex-wrap: wrap;
}

.btn-light {
  background: var(--white);
  color: var(--primary-dark);
  border-color: var(--white);
  font-weight: var(--font-semibold);
  box-shadow: var(--shadow-lg);
}

.btn-light:hover {
  background: var(--gray-100);
  color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-outline-light {
  background: transparent;
  color: var(--white);
  border-color: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
}

.btn-outline-light:hover {
  background: var(--white);
  color: var(--primary-dark);
  border-color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/*
===========================================
MODERN SEARCH SECTION
===========================================
*/

.search-section {
  background: var(--white);
  padding: var(--space-16) 0;
  border-bottom: 1px solid var(--gray-200);
  position: relative;
}

.search-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
}

.search-box {
  position: relative;
  max-width: 700px;
  margin: 0 auto;
}

.search-box input {
  width: 100%;
  padding: var(--space-5) var(--space-6) var(--space-5) var(--space-16);
  font-size: 1.125rem;
  font-weight: var(--font-medium);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-3xl);
  background: var(--white);
  color: var(--gray-800);
  transition: var(--transition-all);
  box-shadow: var(--shadow-sm);
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.1), var(--shadow-lg);
  transform: translateY(-2px);
}

.search-box input::placeholder {
  color: var(--gray-500);
  font-weight: var(--font-normal);
}

.search-box i {
  position: absolute;
  left: var(--space-6);
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-blue);
  font-size: 1.25rem;
  z-index: 10;
}

/* Modern Country Dropdown */
.country-dropdown-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: var(--space-2);
}

.country-dropdown {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
  max-height: 400px;
  backdrop-filter: blur(20px);
}

.country-dropdown-header {
  padding: var(--space-4) var(--space-6);
  background: var(--gradient-light);
  border-bottom: 1px solid var(--gray-200);
}

.country-dropdown-header h5 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: var(--font-semibold);
  color: var(--gray-700);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.country-dropdown-body {
  max-height: 320px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-blue) var(--gray-100);
}

.country-dropdown-body::-webkit-scrollbar {
  width: 6px;
}

.country-dropdown-body::-webkit-scrollbar-track {
  background: var(--gray-100);
}

.country-dropdown-body::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 3px;
}

.country-list {
  padding: var(--space-2) 0;
}

.country-dropdown-item {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-6);
  cursor: pointer;
  transition: var(--transition-all);
  border-left: 3px solid transparent;
}

.country-dropdown-item:hover {
  background: var(--gray-50);
  border-left-color: var(--accent-orange);
  transform: translateX(4px);
}

.country-flag-small {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-right: var(--space-3);
  box-shadow: var(--shadow-sm);
  flex-shrink: 0;
}

.country-flag-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.country-flag-small i {
  color: var(--primary-blue);
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: var(--gray-100);
}

/*
===========================================
MODERN SECTION TITLES
===========================================
*/

.section-title {
  font-family: var(--font-family-display);
  font-size: 3rem;
  font-weight: var(--font-extrabold);
  text-align: center;
  margin-bottom: var(--space-16);
  color: var(--gray-900);
  position: relative;
  letter-spacing: -0.05em;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -var(--space-4);
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: var(--gradient-accent);
  border-radius: var(--radius-full);
}

/*
===========================================
MODERN COUNTRY SECTION
===========================================
*/

.country-section {
  padding: var(--space-24) 0;
  background: var(--white);
}

.country-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  transition: all 0.4s var(--ease-out-expo);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* 3D Hover Effect */
.country-card:hover {
  transform: translateY(-12px) rotateX(5deg) rotateY(5deg);
  box-shadow: var(--shadow-2xl), var(--shadow-primary);
  border-color: var(--primary-blue);
}

/* Glassmorphism Overlay */
.country-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--glass-bg);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.country-card:hover::after {
  opacity: 1;
}

.country-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
  transform: scaleX(0);
  transition: var(--transition-all);
}

.country-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-blue);
}

.country-card:hover::before {
  transform: scaleX(1);
}

.country-flag {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-transform);
  position: relative;
}

.country-card:hover .country-flag {
  transform: scale(1.1) rotate(5deg);
}

.country-flag img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.country-flag i {
  color: var(--primary-blue);
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: var(--gradient-light);
}

.country-name {
  font-family: var(--font-family-display);
  font-size: 1.25rem;
  font-weight: var(--font-bold);
  color: var(--gray-900);
  margin-bottom: var(--space-2);
}

.country-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.country-link:hover {
  color: inherit;
}

.highlight-country {
  border-color: var(--accent-orange);
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

.highlight-country::before {
  transform: scaleX(1);
}

/*
===========================================
MODERN PACKAGE CARDS
===========================================
*/

.package-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-3xl);
  overflow: hidden;
  transition: var(--transition-all);
  cursor: pointer;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.package-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: var(--gradient-accent);
  transform: scaleX(0);
  transition: var(--transition-all);
}

.package-card:hover {
  transform: translateY(-16px) scale(1.02);
  box-shadow: var(--shadow-2xl), var(--shadow-primary);
  border-color: var(--primary-blue);
}

/* Premium Package Glow Effect */
.package-card.premium {
  position: relative;
  overflow: visible;
}

.package-card.premium::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--gradient-accent);
  border-radius: var(--radius-3xl);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.package-card.premium:hover::before {
  opacity: 1;
  animation: premiumGlow 2s ease-in-out infinite alternate;
}

@keyframes premiumGlow {
  0% {
    box-shadow: var(--neon-blue);
    transform: scale(1);
  }
  100% {
    box-shadow: var(--neon-orange);
    transform: scale(1.01);
  }
}

.package-card:hover::before {
  transform: scaleX(1);
}

.package-card .card-header {
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--space-8);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.package-card .card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(245, 158, 11, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.package-card .card-header h4 {
  position: relative;
  z-index: 2;
  margin: 0;
  font-family: var(--font-family-display);
  font-size: 1.5rem;
  font-weight: var(--font-bold);
  color: var(--white);
  letter-spacing: -0.025em;
}

.package-card .card-body {
  padding: var(--space-8);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.package-feature {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-4);
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius-xl);
  border-left: 4px solid var(--primary-blue);
  transition: var(--transition-all);
}

.package-feature:hover {
  background: rgba(30, 64, 175, 0.05);
  transform: translateX(4px);
  border-left-color: var(--accent-orange);
}

.package-feature i {
  color: var(--primary-blue);
  font-size: 1.25rem;
  width: 32px;
  margin-right: var(--space-4);
  text-align: center;
  flex-shrink: 0;
}

.package-feature span {
  flex: 1;
  font-weight: var(--font-medium);
  color: var(--gray-700);
  font-size: 0.9rem;
}

.coverage-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}

.coverage-details-link {
  font-size: 0.75rem;
  color: var(--primary-blue);
  text-decoration: none;
  font-weight: var(--font-semibold);
  margin-left: var(--space-3);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  background: rgba(30, 64, 175, 0.1);
  border: 1px solid rgba(30, 64, 175, 0.2);
  transition: var(--transition-all);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  white-space: nowrap;
}

.coverage-details-link:hover {
  background: var(--primary-blue);
  color: var(--white);
  border-color: var(--primary-blue);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.package-price {
  font-family: var(--font-family-display);
  font-size: 2.5rem;
  font-weight: var(--font-extrabold);
  color: var(--primary-dark);
  text-align: center;
  margin: var(--space-6) 0;
  position: relative;
  letter-spacing: -0.05em;
}

.package-price::before {
  content: '';
  position: absolute;
  bottom: -var(--space-2);
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--gradient-accent);
  border-radius: var(--radius-full);
}

.package-price small {
  font-size: 0.875rem;
  color: var(--gray-500);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/*
===========================================
MODERN BEST SELLERS SECTION
===========================================
*/

.best-sellers {
  padding: var(--space-24) 0;
  background: var(--gradient-light);
  position: relative;
  overflow: hidden;
}

.best-sellers::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(30, 64, 175, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(245, 158, 11, 0.03) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

.best-sellers .container {
  position: relative;
  z-index: 2;
}

.best-seller-badge {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: var(--gradient-accent);
  color: var(--white);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: var(--font-bold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  z-index: 10;
  box-shadow: var(--shadow-lg);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/*
===========================================
MODERN FORMS
===========================================
*/

.form-control {
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-xl);
  padding: var(--space-4) var(--space-5);
  font-size: 1rem;
  font-weight: var(--font-medium);
  transition: var(--transition-all);
  background: var(--white);
  color: var(--gray-800);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.1);
  transform: translateY(-1px);
}

.form-control::placeholder {
  color: var(--gray-500);
  font-weight: var(--font-normal);
}

.form-label {
  font-weight: var(--font-semibold);
  color: var(--gray-800);
  margin-bottom: var(--space-2);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.form-select {
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-xl);
  padding: var(--space-4) var(--space-5);
  font-size: 1rem;
  font-weight: var(--font-medium);
  transition: var(--transition-all);
  background: var(--white);
  color: var(--gray-800);
}

.form-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.1);
}

/*
===========================================
MODERN FOOTER
===========================================
*/

.footer {
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--space-24) 0 var(--space-8);
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  animation: float 30s ease-in-out infinite;
}

.footer .container {
  position: relative;
  z-index: 2;
}

.footer-logo {
  margin-bottom: var(--space-6);
}

.footer-logo img {
  height: 56px;
  filter: brightness(0) invert(1);
  transition: var(--transition-transform);
}

.footer-logo img:hover {
  transform: scale(1.05) rotate(2deg);
}

.footer h5 {
  font-family: var(--font-family-display);
  font-weight: var(--font-bold);
  font-size: 1.25rem;
  color: var(--white);
  margin-bottom: var(--space-6);
  position: relative;
  letter-spacing: -0.025em;
}

.footer h5::after {
  content: '';
  position: absolute;
  bottom: -var(--space-2);
  left: 0;
  width: 40px;
  height: 3px;
  background: var(--gradient-accent);
  border-radius: var(--radius-full);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: var(--space-3);
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  font-weight: var(--font-medium);
  transition: var(--transition-all);
  position: relative;
  display: inline-block;
}

.footer-links a:hover {
  color: var(--white);
  transform: translateX(4px);
}

.footer-links a::before {
  content: '';
  position: absolute;
  left: -var(--space-4);
  top: 50%;
  transform: translateY(-50%) scaleX(0);
  width: 12px;
  height: 2px;
  background: var(--accent-orange);
  transition: var(--transition-all);
}

.footer-links a:hover::before {
  transform: translateY(-50%) scaleX(1);
}

.social-links {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-6);
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border-radius: var(--radius-2xl);
  transition: var(--transition-all);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-links a:hover {
  background: var(--accent-orange);
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--accent-orange);
}

.footer-bottom {
  margin-top: var(--space-16);
  padding-top: var(--space-8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.footer-bottom p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-size: 0.875rem;
}

/*
===========================================
MODERN ALERTS & NOTIFICATIONS
===========================================
*/

.alert {
  border: none;
  border-radius: var(--radius-2xl);
  padding: var(--space-4) var(--space-6);
  font-weight: var(--font-medium);
  border-left: 4px solid;
  position: relative;
  overflow: hidden;
}

.alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: currentColor;
  opacity: 0.3;
}

.alert-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
  border-left-color: var(--success);
}

.alert-info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info);
  border-left-color: var(--info);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
  border-left-color: var(--warning);
}

.alert-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error);
  border-left-color: var(--error);
}

/*
===========================================
MODERN BADGES
===========================================
*/

.badge {
  font-weight: var(--font-semibold);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
}

.bg-primary {
  background: var(--gradient-primary) !important;
  color: var(--white) !important;
}

.bg-success {
  background: linear-gradient(135deg, var(--success) 0%, #059669 100%) !important;
  color: var(--white) !important;
}

.bg-warning {
  background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%) !important;
  color: var(--white) !important;
}

/*
===========================================
MODERN MODALS
===========================================
*/

.modal-content {
  border: none;
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.modal-header {
  background: var(--gradient-primary);
  color: var(--white);
  border-bottom: none;
  padding: var(--space-6) var(--space-8);
  position: relative;
}

.modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 30% 30%, rgba(245, 158, 11, 0.2) 0%, transparent 50%);
}

.modal-title {
  font-family: var(--font-family-display);
  font-weight: var(--font-bold);
  font-size: 1.5rem;
  position: relative;
  z-index: 2;
  letter-spacing: -0.025em;
}

.btn-close {
  filter: brightness(0) invert(1);
  opacity: 0.8;
  transition: var(--transition-all);
}

.btn-close:hover {
  opacity: 1;
  transform: scale(1.1);
}

.modal-body {
  padding: var(--space-8);
  background: var(--white);
}

.modal-footer {
  border-top: 1px solid var(--gray-200);
  padding: var(--space-6) var(--space-8);
  background: var(--gray-50);
}

/*
===========================================
MODERN RESPONSIVE DESIGN
===========================================
*/

/* Large Screens (Desktop) */
@media (max-width: 1200px) {
  .hero-section h1 {
    font-size: 4rem;
  }

  .section-title {
    font-size: 2.5rem;
  }
}

/* Medium Screens (Tablet) */
@media (max-width: 992px) {
  .hero-section {
    padding: var(--space-20) 0;
    text-align: center;
    min-height: 80vh;
  }

  .hero-section h1 {
    font-size: 3.5rem;
  }

  .hero-section p {
    font-size: 1.125rem;
  }

  .hero-image {
    margin-top: var(--space-8);
    max-width: 80%;
  }

  .navbar-collapse {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    margin-top: var(--space-4);
    padding: var(--space-6);
    border: 1px solid var(--gray-200);
  }

  .section-title {
    font-size: 2.25rem;
  }

  .country-card {
    padding: var(--space-6);
  }

  .package-card .card-body {
    padding: var(--space-6);
  }

  .search-box input {
    font-size: 1rem;
    padding: var(--space-4) var(--space-5) var(--space-4) var(--space-12);
  }

  .search-box i {
    left: var(--space-4);
    font-size: 1.125rem;
  }
}

/* Small Screens (Mobile) */
@media (max-width: 768px) {
  .hero-section {
    padding: var(--space-16) 0;
    min-height: 70vh;
  }

  .hero-section h1 {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .hero-section p {
    font-size: 1rem;
  }

  .section-title {
    font-size: 2rem;
    margin-bottom: var(--space-12);
  }

  .country-flag {
    width: 64px;
    height: 64px;
  }

  .package-price {
    font-size: 2rem;
  }

  .country-section,
  .best-sellers {
    padding: var(--space-16) 0;
  }

  .search-section {
    padding: var(--space-12) 0;
  }

  .footer {
    padding: var(--space-16) 0 var(--space-6);
  }

  .social-links {
    justify-content: center;
  }

  .hero-cta {
    justify-content: center;
  }

  .btn-lg {
    padding: var(--space-3) var(--space-6);
    font-size: 0.875rem;
  }
}

/* Extra Small Screens */
@media (max-width: 576px) {
  .hero-section h1 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .country-card {
    padding: var(--space-4);
  }

  .package-card .card-body,
  .card-body {
    padding: var(--space-4);
  }

  .package-card .card-header {
    padding: var(--space-6);
  }

  .country-flag {
    width: 56px;
    height: 56px;
  }

  .package-price {
    font-size: 1.75rem;
  }

  .search-box input {
    padding: var(--space-3) var(--space-4) var(--space-3) var(--space-10);
  }

  .search-box i {
    left: var(--space-3);
    font-size: 1rem;
  }

  .modal-body,
  .modal-header,
  .modal-footer {
    padding: var(--space-4) var(--space-5);
  }
}

/*
===========================================
MODERN UTILITY CLASSES
===========================================
*/

/* Spacing Utilities */
.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--space-1) !important; }
.mt-2 { margin-top: var(--space-2) !important; }
.mt-3 { margin-top: var(--space-3) !important; }
.mt-4 { margin-top: var(--space-4) !important; }
.mt-5 { margin-top: var(--space-5) !important; }
.mt-6 { margin-top: var(--space-6) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--space-1) !important; }
.mb-2 { margin-bottom: var(--space-2) !important; }
.mb-3 { margin-bottom: var(--space-3) !important; }
.mb-4 { margin-bottom: var(--space-4) !important; }
.mb-5 { margin-bottom: var(--space-5) !important; }
.mb-6 { margin-bottom: var(--space-6) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--space-1) !important; }
.pt-2 { padding-top: var(--space-2) !important; }
.pt-3 { padding-top: var(--space-3) !important; }
.pt-4 { padding-top: var(--space-4) !important; }
.pt-5 { padding-top: var(--space-5) !important; }
.pt-6 { padding-top: var(--space-6) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--space-1) !important; }
.pb-2 { padding-bottom: var(--space-2) !important; }
.pb-3 { padding-bottom: var(--space-3) !important; }
.pb-4 { padding-bottom: var(--space-4) !important; }
.pb-5 { padding-bottom: var(--space-5) !important; }
.pb-6 { padding-bottom: var(--space-6) !important; }

/* Text Utilities */
.text-primary { color: var(--primary-blue) !important; }
.text-dark { color: var(--primary-dark) !important; }
.text-accent { color: var(--accent-orange) !important; }
.text-success { color: var(--success) !important; }
.text-warning { color: var(--warning) !important; }
.text-danger { color: var(--error) !important; }

.fw-light { font-weight: var(--font-light) !important; }
.fw-normal { font-weight: var(--font-normal) !important; }
.fw-medium { font-weight: var(--font-medium) !important; }
.fw-semibold { font-weight: var(--font-semibold) !important; }
.fw-bold { font-weight: var(--font-bold) !important; }
.fw-extrabold { font-weight: var(--font-extrabold) !important; }

/* Shadow Utilities */
.shadow-xs { box-shadow: var(--shadow-xs) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-2xl { box-shadow: var(--shadow-2xl) !important; }

/* Border Radius Utilities */
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-2xl { border-radius: var(--radius-2xl) !important; }
.rounded-3xl { border-radius: var(--radius-3xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/*
===========================================
ACCESSIBILITY & PERFORMANCE
===========================================
*/

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .btn {
    border-width: 3px;
  }

  .card {
    border-width: 2px;
  }

  .form-control,
  .form-select {
    border-width: 3px;
  }
}

/* Print Styles */
@media print {
  .navbar,
  .footer,
  .btn,
  .modal,
  .search-section {
    display: none !important;
  }

  .hero-section {
    background: none !important;
    color: var(--black) !important;
    padding: var(--space-4) 0 !important;
  }

  .package-card,
  .country-card,
  .card {
    box-shadow: none !important;
    border: 2px solid var(--gray-400) !important;
    break-inside: avoid;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  h1, h2, h3, h4, h5, h6 {
    color: var(--black) !important;
    page-break-after: avoid;
  }
}

/* Focus Visible Support */
.btn:focus-visible,
.form-control:focus-visible,
.form-select:focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/*
===========================================
MODERN ANIMATIONS
===========================================
*/

.animate-fade-in {
  animation: fadeIn 0.8s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/*
===========================================
ULTRA MODERN SPECIAL EFFECTS
===========================================
*/

/* Holographic Effect */
.holographic {
  background: linear-gradient(45deg, #ff0000, #ff7300, #fffb00, #48ff00, #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000);
  background-size: 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: holographic 3s linear infinite;
}

@keyframes holographic {
  0% { background-position: 0% 50%; }
  100% { background-position: 400% 50%; }
}

/* Morphing Shapes */
.morphing-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-accent);
  clip-path: polygon(0% 0%, 100% 0%, 100% 85%, 0% 100%);
  animation: morphing 8s ease-in-out infinite;
}

@keyframes morphing {
  0%, 100% { clip-path: polygon(0% 0%, 100% 0%, 100% 85%, 0% 100%); }
  25% { clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 85%); }
  50% { clip-path: polygon(0% 15%, 100% 0%, 100% 100%, 0% 85%); }
  75% { clip-path: polygon(0% 0%, 100% 15%, 100% 85%, 0% 100%); }
}

/* Particle Effect */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--accent-orange);
  border-radius: 50%;
  animation: float-particle 6s linear infinite;
  opacity: 0;
}

@keyframes float-particle {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(100px);
    opacity: 0;
  }
}

/* Liquid Animation */
.liquid-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-ocean);
  border-radius: 50%;
  animation: liquid 10s ease-in-out infinite;
  transform-origin: center;
}

@keyframes liquid {
  0%, 100% {
    border-radius: 50% 40% 30% 70% / 60% 30% 70% 40%;
    transform: scale(1) rotate(0deg);
  }
  25% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    transform: scale(1.1) rotate(90deg);
  }
  50% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: scale(0.9) rotate(180deg);
  }
  75% {
    border-radius: 40% 60% 70% 30% / 40% 70% 60% 50%;
    transform: scale(1.05) rotate(270deg);
  }
}

/* Glitch Effect */
.glitch {
  position: relative;
  color: var(--white);
  font-weight: var(--font-bold);
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: #ff0000;
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: #00ffff;
  z-index: -2;
}

@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: translate(0);
  }
  15%, 49% {
    transform: translate(-2px, 2px);
  }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% {
    transform: translate(0);
  }
  21%, 62% {
    transform: translate(2px, -2px);
  }
}

/* Cyberpunk Glow */
.cyberpunk-glow {
  position: relative;
  background: var(--gradient-primary);
  border: 2px solid var(--accent-orange);
  box-shadow:
    0 0 10px var(--accent-orange),
    inset 0 0 10px rgba(245, 158, 11, 0.1);
  animation: cyberpunk-pulse 2s ease-in-out infinite alternate;
}

@keyframes cyberpunk-pulse {
  0% {
    box-shadow:
      0 0 10px var(--accent-orange),
      inset 0 0 10px rgba(245, 158, 11, 0.1);
  }
  100% {
    box-shadow:
      0 0 20px var(--accent-orange),
      0 0 30px var(--accent-orange),
      inset 0 0 20px rgba(245, 158, 11, 0.2);
  }
}

/* Matrix Rain Effect */
.matrix-rain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  overflow: hidden;
  pointer-events: none;
}

.matrix-char {
  position: absolute;
  color: var(--success);
  font-family: 'JetBrains Mono', monospace;
  font-size: 14px;
  animation: matrix-fall 3s linear infinite;
  opacity: 0;
}

@keyframes matrix-fall {
  0% {
    transform: translateY(-100px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

/* Floating Elements */
.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Pulse Effect */
.pulse {
  animation: pulse-effect 2s infinite;
}

@keyframes pulse-effect {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Shake Effect */
.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Bounce In Effect */
.bounce-in {
  animation: bounce-in-effect 0.6s var(--bounce-in);
}

@keyframes bounce-in-effect {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  70% {
    transform: scale(0.9);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Gradient Text Animation */
.gradient-text-animated {
  background: var(--gradient-rainbow);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
