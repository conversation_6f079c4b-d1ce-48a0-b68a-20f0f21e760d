<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    protected $fillable = [
        'payment_id',
        'status',
        'order_no',
        'pay_address',
        'pay_amount',
        'pay_currency',
        'price_amount',
        'price_currency',
        'order_id',
        'package_code',
        'email',
        'name',
        'network',
        'expiration_estimate_date',
        'merchant_reference_id',
        'rapyd_checkout_id',
        'payment_method',
        'esim_order_no',
        'esim_iccid',
        'esim_activation_code',
        'esim_pin',
        'esim_puk',
        'esim_qr_url',
        'esim_tran_no'
    ];

    protected $dates = [
        'expiration_estimate_date',
    ];
}
