<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class EsimPurchaseConfirmation extends Mailable
{
    use Queueable, SerializesModels;

    public $orderDetails;

    public function __construct($orderDetails)
    {
        $this->orderDetails = $orderDetails;
    }

    public function build()
    {
        $mail = $this->subject('VOX eSIM - Your eSIM Order Details')
                    ->view('emails.esim-purchase');

        // QR code attachment if exists
        if (!empty($this->orderDetails['qrCodeImage'])) {
            $mail->attachData(
                $this->orderDetails['qrCodeImage'],
                'esim-qr-code.png',
                ['mime' => 'image/png']
            );
        }

        // Parse activation code from 'ac' field if not already parsed
        if (!empty($this->orderDetails['ac']) && empty($this->orderDetails['activationCode'])) {
            $this->orderDetails['activationCode'] = $this->orderDetails['ac'];
        }

        return $mail;
    }
}



