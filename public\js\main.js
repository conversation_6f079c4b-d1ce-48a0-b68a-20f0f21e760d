/**
 * Vox eSIM - Ultra Modern JavaScript
 * Version: 5.0 - Complete Redesign
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize ultra modern components
    initUltraModernEffects();
    initLanguageSwitcher();
    initAdvancedAnimations();
    initMobileMenu();
    initBestSellers();
    initSearch();
    initPackageFilters();
    initParallaxEffects();
    initGlassmorphism();
    initScrollEffects();

    console.log('🚀 Vox eSIM Ultra Modern v5.0 initialized successfully');
});

// Ultra Modern Effects
function initUltraModernEffects() {
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // 3D Tilt Effect for Cards
    const cards = document.querySelectorAll('.country-card, .package-card');
    cards.forEach(card => {
        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;

            this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(20px)`;
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
        });
    });

    // Magnetic Button Effect
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(btn => {
        btn.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            this.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px) scale(1.05)`;
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translate(0, 0) scale(1)';
        });
    });
}

// Advanced Parallax Effects
function initParallaxEffects() {
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.hero-section');
        if (parallax) {
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        }
    });
}

// Glassmorphism Effects
function initGlassmorphism() {
    // Add glassmorphism to modals and dropdowns
    const elements = document.querySelectorAll('.modal-content, .dropdown-menu, .country-dropdown');
    elements.forEach(el => {
        el.classList.add('glass-effect');
    });
}

// Advanced Scroll Effects
function initScrollEffects() {
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
            }
        });
    }, observerOptions);

    // Observe all cards and sections
    const elements = document.querySelectorAll('.country-card, .package-card, .section-title');
    elements.forEach(el => {
        observer.observe(el);
    });
}

    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                e.preventDefault();
                window.scrollTo({
                    top: targetElement.offsetTop - 80,
                    behavior: 'smooth'
                });
            }
        });
    });
});

/**
 * Initialize language switcher functionality
 */
function initLanguageSwitcher() {
    // Artık dil değiştirme işlemi sunucu tarafında gerçekleşiyor
    // Bu fonksiyon sadece yer tutucu olarak bırakılmıştır
    console.log('Language switcher initialized');
}

/**
 * Initialize animations for elements - Simplified
 */
function initAnimations() {
    // Add fade-in animation to elements with .animate-fade-in class
    const fadeElements = document.querySelectorAll('.animate-fade-in');
    fadeElements.forEach(element => {
        const observer = new IntersectionObserver(entries => {
            if (entries[0].isIntersecting) {
                element.classList.add('fade-in');
                observer.unobserve(element);
            }
        }, { threshold: 0.2 });

        observer.observe(element);
    });

    // Add slide-up animation to elements with .animate-slide-up class
    const slideElements = document.querySelectorAll('.animate-slide-up');
    slideElements.forEach(element => {
        const observer = new IntersectionObserver(entries => {
            if (entries[0].isIntersecting) {
                element.classList.add('slide-up');
                observer.unobserve(element);
            }
        }, { threshold: 0.2 });

        observer.observe(element);
    });
}

/**
 * Initialize mobile menu functionality
 */
function initMobileMenu() {
    const menuToggle = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (!menuToggle || !navbarCollapse) return;

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        const isClickInside = navbarCollapse.contains(e.target) || menuToggle.contains(e.target);

        if (!isClickInside && navbarCollapse.classList.contains('show')) {
            // Using Bootstrap's collapse API
            const bsCollapse = new bootstrap.Collapse(navbarCollapse);
            bsCollapse.hide();
        }
    });

    // Close menu when clicking on a nav link (for mobile)
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth < 992 && navbarCollapse.classList.contains('show')) {
                // Using Bootstrap's collapse API
                const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                bsCollapse.hide();
            }
        });
    });
}

/**
 * Initialize best sellers section - Simplified
 */
function initBestSellers() {
    // Simplified version without custom hover effects
    // CSS handles hover effects now for a more consistent look
    console.log('Best sellers section initialized');
}

/**
 * Initialize search functionality - Simplified
 */
function initSearch() {
    const searchInput = document.getElementById('search-input');
    if (!searchInput) return;

    const dropdownContainer = document.querySelector('.country-dropdown-container');
    const countryDropdownItems = document.querySelectorAll('.country-dropdown-item');

    // Show dropdown when focusing on search input
    searchInput.addEventListener('focus', function() {
        dropdownContainer.style.display = 'block';
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        const isClickInside = searchInput.contains(e.target) || dropdownContainer.contains(e.target);
        if (!isClickInside && dropdownContainer.style.display === 'block') {
            dropdownContainer.style.display = 'none';
        }
    });

    // Filter countries in dropdown as user types
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();

        // Show dropdown when typing
        if (dropdownContainer.style.display === 'none') {
            dropdownContainer.style.display = 'block';
        }

        // Filter dropdown items
        countryDropdownItems.forEach(item => {
            const countryName = item.getAttribute('data-country').toLowerCase();
            item.style.display = countryName.includes(searchTerm) || searchTerm === '' ? 'flex' : 'none';
        });

        // Also filter the main country grid
        const countryItems = document.querySelectorAll('.country-item');
        const noResultsElement = document.getElementById('no-results');
        let gridResultsFound = false;

        countryItems.forEach(item => {
            const countryName = item.querySelector('.country-name').textContent.toLowerCase();
            const shouldShow = countryName.includes(searchTerm) || searchTerm === '';
            item.style.display = shouldShow ? 'block' : 'none';
            if (shouldShow) gridResultsFound = true;
        });

        // Show or hide the "no results" message for the grid
        if (noResultsElement) {
            noResultsElement.style.display = (!gridResultsFound && searchTerm !== '') ? 'block' : 'none';
        }
    });

    // Handle country selection from dropdown
    countryDropdownItems.forEach(item => {
        item.addEventListener('click', function() {
            const countryName = this.getAttribute('data-country');

            // Update search input with selected country
            searchInput.value = countryName;

            // Hide dropdown
            dropdownContainer.style.display = 'none';

            // Filter the grid to show only the selected country
            const countryItems = document.querySelectorAll('.country-item');
            const noResultsElement = document.getElementById('no-results');
            let found = false;

            countryItems.forEach(gridItem => {
                const gridCountryName = gridItem.querySelector('.country-name').textContent;
                const isMatch = gridCountryName === countryName;

                gridItem.style.display = isMatch ? 'block' : 'none';

                if (isMatch) {
                    found = true;
                    // Scroll to the country card with simpler highlight
                    setTimeout(() => {
                        gridItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        gridItem.querySelector('.country-card').classList.add('highlight-country');

                        // Remove highlight after a shorter time
                        setTimeout(() => {
                            gridItem.querySelector('.country-card').classList.remove('highlight-country');
                        }, 1000);
                    }, 100);
                }
            });

            // Show no results message if country not found in grid
            if (noResultsElement) {
                noResultsElement.style.display = found ? 'none' : 'block';
            }
        });
    });
}

/**
 * Initialize package filters - Simplified
 */
function initPackageFilters() {
    const dataFilter = document.getElementById('dataFilter');
    const durationFilter = document.getElementById('durationFilter');
    const priceFilter = document.getElementById('priceFilter');
    const resetFilters = document.getElementById('resetFilters');

    if (!dataFilter && !durationFilter && !priceFilter) return;

    // Data filter
    if (dataFilter) {
        dataFilter.addEventListener('change', function() {
            filterPackages('data', this.value);
        });
    }

    // Duration filter
    if (durationFilter) {
        durationFilter.addEventListener('change', function() {
            filterPackages('duration', this.value);
        });
    }

    // Price filter
    if (priceFilter) {
        priceFilter.addEventListener('change', function() {
            filterPackages('price', this.value);
        });
    }

    // Reset filters
    if (resetFilters) {
        resetFilters.addEventListener('click', function() {
            if (dataFilter) dataFilter.value = 'all';
            if (durationFilter) durationFilter.value = 'all';
            if (priceFilter) priceFilter.value = 'all';

            // Show all packages
            showAllPackages();
        });
    }
}

/**
 * Show all packages
 */
function showAllPackages() {
    const packageCards = document.querySelectorAll('.package-card');
    packageCards.forEach(card => {
        const parentCol = card.closest('.col-md-4');
        if (parentCol) parentCol.style.display = 'block';
    });
}

/**
 * Filter packages by data amount, duration, or price - Simplified
 * @param {string} filterType - Type of filter (data, duration, price)
 * @param {string} value - Filter value
 */
function filterPackages(filterType, value) {
    if (value === 'all') {
        showAllPackages();
        return;
    }

    const packageCards = document.querySelectorAll('.package-card');

    packageCards.forEach(card => {
        const parentCol = card.closest('.col-md-4');
        if (!parentCol) return;

        let shouldShow = false;

        if (filterType === 'data') {
            const dataText = card.querySelector('.package-feature:nth-child(1)').textContent;
            const dataValue = parseFloat(dataText.match(/\d+(\.\d+)?/)?.[0] || 0);

            switch (value) {
                case '1GB': shouldShow = dataValue <= 1; break;
                case '5GB': shouldShow = dataValue > 1 && dataValue <= 5; break;
                case '10GB': shouldShow = dataValue > 5 && dataValue <= 10; break;
                case 'unlimited': shouldShow = dataValue > 10 || dataText.toLowerCase().includes('unlimited'); break;
            }
        } else if (filterType === 'duration') {
            const durationText = card.querySelector('.package-feature:nth-child(2)').textContent;
            const durationValue = parseInt(durationText.match(/\d+/)?.[0] || 0);

            switch (value) {
                case '7': shouldShow = durationValue <= 7; break;
                case '15': shouldShow = durationValue > 7 && durationValue <= 15; break;
                case '30': shouldShow = durationValue > 15 && durationValue <= 30; break;
                case '31': shouldShow = durationValue > 30; break;
            }
        } else if (filterType === 'price') {
            const priceText = card.querySelector('.package-price').textContent;
            const priceValue = parseFloat(priceText.match(/\d+(\.\d+)?/)?.[0] || 0);

            switch (value) {
                case 'budget': shouldShow = priceValue <= 15; break;
                case 'standard': shouldShow = priceValue > 15 && priceValue <= 30; break;
                case 'premium': shouldShow = priceValue > 30; break;
            }
        }

        parentCol.style.display = shouldShow ? 'block' : 'none';
    });
}
