<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\File;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Fallback definition for t() function if it doesn't exist
        if (!function_exists('t')) {
            function t($key = null, $replace = [], $locale = null) {
                return \App\Helpers\TranslationHelper::trans($key, $replace, $locale);
            }
        }

        // Dil dosyalarının yolunu belirle
        $langPath = base_path('lang');

        // Log language file loading
        if (File::exists($langPath)) {
            $this->loadTranslationsFrom($langPath, 'messages');
        }
    }
}

