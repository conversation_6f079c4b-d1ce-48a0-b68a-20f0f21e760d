<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f9fafb;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
        }
        .header {
            text-align: center;
            padding: 30px 0;
            background-color: #1a56db;
            color: white;
            border-radius: 8px 8px 0 0;
        }
        .content {
            padding: 30px;
            background: #ffffff;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .package-details {
            background: #f8fafc;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        .qr-code {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #ffffff;
            border-radius: 8px;
        }
        .qr-code img {
            max-width: 250px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px;
        }
        .important-info {
            color: #dc2626;
            font-weight: 600;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .detail-label {
            color: #6b7280;
            font-weight: 500;
        }
        .detail-value {
            color: #1f2937;
            font-weight: 600;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #1a56db;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin-top: 20px;
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #6b7280;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Your eSIM Order Details</h2>
        </div>
        
        <div class="content">
            <p>Dear {{ $orderDetails['customerName'] }},</p>
            
            <p>Thank you for your eSIM purchase! Your order has been successfully processed. Below are your eSIM details:</p>
            
            <div class="package-details">
                <h3>Package Information</h3>
                <div class="detail-row">
                    <span class="detail-label">Package Name:</span>
                    <span class="detail-value">{{ $orderDetails['packageName'] }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Data Allowance:</span>
                    <span class="detail-value">{{ $orderDetails['data'] }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Validity Period:</span>
                    <span class="detail-value">{{ $orderDetails['validity'] }} days</span>
                </div>
                
                <h3>eSIM Details</h3>
                <div class="detail-row">
                    <span class="detail-label">ICCID:</span>
                    <span class="detail-value">{{ $orderDetails['iccid'] }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">PIN:</span>
                    <span class="detail-value">{{ $orderDetails['pin'] }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">PUK:</span>
                    <span class="detail-value">{{ $orderDetails['puk'] }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">APN:</span>
                    <span class="detail-value">{{ $orderDetails['apn'] }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Expiration Date:</span>
                    <span class="detail-value">{{ \Carbon\Carbon::parse($orderDetails['expiredTime'])->format('d.m.Y H:i') }}</span>
                </div>
            </div>

            @if(!empty($orderDetails['qrCodeImage']))
            <div class="qr-code">
                <h3>eSIM QR Code</h3>
                
                <p>Click the button below to view your eSIM QR code:</p>
                <a href="{{ $orderDetails['qrCodeUrl'] }}" 
                   class="button"
                   style="background-color: #1a56db; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; display: inline-block; margin: 20px auto; font-weight: 500; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: all 0.3s ease;">
                    <i style="margin-right: 8px;">🔍</i> View QR Code
                </a>
            </div>

            <div class="package-details">
                <h3>Installation Instructions</h3>
                
                <h4>Method 1: QR Code Installation (Recommended)</h4>
                <ol style="padding-left: 20px;">
                    <li>Go to your phone's Settings</li>
                    <li>Select Mobile Data > Add eSIM</li>
                    <li>Scan the QR code above</li>
                    <li>If prompted, use PIN: <strong>{{ $orderDetails['pin'] }}</strong></li>
                </ol>

                <h4>Method 2: Manual Installation</h4>
                <p>If QR code scanning doesn't work, you can enter these details manually:</p>
                <div class="manual-details" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <p><strong>SM-DP+ Address:</strong> <code>{{ explode('$', $orderDetails['activationCode'])[1] }}</code></p>
                    <p><strong>Activation Code:</strong> <code>{{ explode('$', $orderDetails['activationCode'])[2] }}</code></p>
                    <p><strong>Confirmation Code (PIN):</strong> <code>{{ $orderDetails['pin'] }}</code></p>
                </div>

                <ol style="padding-left: 20px;">
                    <li>Go to your phone's Settings</li>
                    <li>Select Mobile Data > Add eSIM</li>
                    <li>Choose "Enter manually" or "Enter details manually"</li>
                    <li>Enter the SM-DP+ Address and Activation Code exactly as shown above</li>
                    <li>When prompted, enter the Confirmation Code (PIN)</li>
                </ol>
            </div>
            @endif

            <p class="important-info">Important: Please save these details for future reference.</p>
            
            <p>If you need any assistance, our support team is here to help.</p>
        </div>

        <div class="footer">
            <p>Best regards,<br>VoxAI Team</p>
            <p style="font-size: 12px; color: #9ca3af;">This is an automated message, please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>









