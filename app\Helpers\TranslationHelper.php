<?php

namespace App\Helpers;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\File;

class TranslationHelper
{
    /**
     * Get translation for a given key
     *
     * @param string $key
     * @param array $replace
     * @param string|null $locale
     * @return string
     */
    public static function trans($key, array $replace = [], $locale = null)
    {
        $locale = $locale ?: App::getLocale();
        $fallbackLocale = config('app.fallback_locale');

        // Try to load the translation file
        $path = base_path("lang/{$locale}/messages.php");

        if (File::exists($path)) {
            $translations = include $path;

            if (isset($translations[$key])) {
                $line = $translations[$key];

                // Replace placeholders
                foreach ($replace as $replaceKey => $value) {
                    $line = str_replace(":{$replaceKey}", $value, $line);
                }

                return $line;
            } else {
                \Log::warning('Translation key not found', [
                    'key' => $key,
                    'locale' => $locale,
                    'available_keys' => array_keys($translations)
                ]);
            }
        } else {
            \Log::error('Translation file not found', [
                'path' => $path,
                'locale' => $locale
            ]);
        }

        // Fallback to default locale
        if ($locale !== $fallbackLocale) {
            return self::trans($key, $replace, $fallbackLocale);
        }

        // Return the key if no translation found
        return $key;
    }
}


