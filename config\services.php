<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'nowpayments' => [
        'api_key' => env('NOWPAYMENTS_API_KEY', 'HAF0C11-3YE45QX-KHFKRAM-BHHJKTN'),
        'public_key' => env('NOWPAYMENTS_PUBLIC_KEY', '82224374-6665-4125-9438-382caf09e15b'),
        'api_url' => 'https://api.nowpayments.io/v1',
    ],

    'esim' => [
        'api_url' => env('ESIM_API_URL', 'https://api.esimaccess.com'),
        'access_code' => env('ESIM_ACCESS_CODE', ''),
    ],

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'stripe' => [
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
    ],

    'rapyd' => [
        'access_key' => env('RAPYD_ACCESS_KEY', 'rak_373B06D2E64D495EB8DF'),
        'secret_key' => env('RAPYD_SECRET_KEY', 'rsk_ead598fda0c46c0d46e3f40acd47d3e86bfec810f76788dd0dda0349ef659596d1d8068877e279ca'),
    ],

];




