<?php $__env->startSection('content'); ?>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<style>
    .expires-cell {
        min-width: 220px;
    }
    
    .expires-cell .badge {
        font-size: 0.75rem;
        padding: 0.35em 0.65em;
    }
    
    /* Add a subtle hover effect to the table rows */
    .table-hover tbody tr:hover {
        background-color: rgba(52, 152, 219, 0.05);
    }
</style>

<div class="container py-5">
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><?php echo e(t('dashboard')); ?></h5>
                </div>
                <div class="card-body">
                    <h4><?php echo e(t('welcome')); ?>, <?php echo e(Auth::user()->name); ?>!</h4>
                    <p class="text-muted"><?php echo e(t('dashboard_intro')); ?></p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- eSIM Sorgulama Kartı -->
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><?php echo e(t('query_esim')); ?></h5>
                </div>
                <div class="card-body">
                    <p><?php echo e(t('query_esim_description')); ?></p>
                    <form id="queryEsimForm" class="mt-3">
                        <div class="input-group mb-3">
                            <input type="text" id="order_no" class="form-control" placeholder="<?php echo e(t('enter_order_number')); ?>" required>
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search me-2"></i><?php echo e(t('query')); ?>

                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Hızlı Bağlantılar Kartı -->
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><?php echo e(t('quick_actions')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('home')); ?>#countries" class="btn btn-outline-primary">
                            <i class="fas fa-globe me-2"></i><?php echo e(t('browse_countries')); ?>

                        </a>
                        <a href="<?php echo e(route('home')); ?>#best-sellers" class="btn btn-outline-primary">
                            <i class="fas fa-star me-2"></i><?php echo e(t('best_selling_plans')); ?>

                        </a>
                        <a href="<?php echo e(route('home')); ?>#support" class="btn btn-outline-primary">
                            <i class="fas fa-headset me-2"></i><?php echo e(t('contact_support')); ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- eSIM Siparişleri -->
    <div class="row mt-2">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><?php echo e(t('your_esims')); ?></h5>
                </div>
                <div class="card-body">
                    <?php if(isset($orders) && count($orders) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo e(t('order_number')); ?></th>
                                        <th><?php echo e(t('date')); ?></th>
                                        <th><?php echo e(t('package')); ?></th>
                                        <th class="status-cell"><?php echo e(t('status')); ?></th>
                                        <th class="expires-cell"><?php echo e(t('expires_on')); ?></th>
                                        <th class="actions-cell"><?php echo e(t('actions')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr data-order-no="<?php echo e($order->order_no); ?>">
                                            <td><?php echo e($order->order_no); ?></td>
                                            <td><?php echo e(is_string($order->created_at) ? $order->created_at : $order->created_at->format('d.m.Y')); ?></td>
                                            <td><?php echo e($order->package_name ?? $order->package ?? 'eSIM Package'); ?></td>
                                            <td class="status-cell">
                                                <span class="badge bg-secondary"><?php echo e(t('loading')); ?>...</span>
                                            </td>
                                            <td class="expires-cell">
                                                <span class="text-muted"><?php echo e(t('loading')); ?>...</span>
                                            </td>
                                            <td class="actions-cell">
                                                <div class="d-flex gap-2">
                                                    <button type="button" class="btn btn-primary btn-sm query-esim" data-order-no="<?php echo e($order->order_no); ?>">
                                                        <i class="fas fa-info-circle"></i> <?php echo e(t('details')); ?>

                                                    </button>
                                                    <button type="button" class="btn btn-info btn-sm resend-email" data-order-no="<?php echo e($order->order_no); ?>">
                                                        <i class="fas fa-envelope"></i> <?php echo e(t('resend_email')); ?>

                                                    </button>
                                                    <?php if($order->esim_tran_no): ?>
                                                    <button type="button" class="btn btn-success btn-sm check-usage" data-esim-tran-no="<?php echo e($order->esim_tran_no); ?>">
                                                        <i class="fas fa-chart-pie"></i> <?php echo e(t('usage')); ?>

                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-sim-card fa-3x text-muted mb-3"></i>
                            <p><?php echo e(t('no_esims_yet')); ?></p>
                            <a href="<?php echo e(route('home')); ?>#countries" class="btn btn-primary mt-2">
                                <?php echo e(t('browse_esim_plans')); ?>

                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Query eSIM Modal -->
<div class="modal fade" id="queryEsimModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><?php echo e(t('query_esim')); ?></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="modalQueryEsimForm">
                    <div class="mb-3">
                        <label for="modal_order_no" class="form-label"><?php echo e(t('order_number')); ?></label>
                        <input type="text" class="form-control" id="modal_order_no" required>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary"><?php echo e(t('query')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- eSIM Detayları Modal -->
<div class="modal fade" id="esimDetailsModal" tabindex="-1" aria-labelledby="esimDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="esimDetailsModalLabel"><?php echo e(t('esim_details')); ?></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-5 text-center mb-4">
                        <div id="qrCodeContainer" class="mb-3">
                            <img id="modalQrCodeImage" src="" alt="eSIM QR Code" class="img-fluid rounded shadow-sm" style="max-width: 200px;">
                        </div>
                        <div class="d-grid gap-2">
                            <a id="downloadQrBtn" href="#" class="btn btn-outline-primary" download="esim-qrcode.png">
                                <i class="fas fa-download me-2"></i><?php echo e(t('download_qr')); ?>

                            </a>
                            <button id="copyActivationCodeBtn" class="btn btn-outline-secondary">
                                <i class="fas fa-copy me-2"></i><?php echo e(t('copy_activation_code')); ?>

                            </button>
                        </div>
                    </div>
                    <div class="col-md-7">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><?php echo e(t('esim_info')); ?></h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-5 text-muted"><?php echo e(t('order_number')); ?>:</div>
                                    <div class="col-7 fw-bold" id="modalOrderNo"></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-5 text-muted"><?php echo e(t('status')); ?>:</div>
                                    <div class="col-7">
                                        <span id="modalEsimStatus" class="badge"></span>
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-5 text-muted"><?php echo e(t('expires_on')); ?>:</div>
                                    <div class="col-7" id="modalExpiredTime"></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-5 text-muted">ICCID:</div>
                                    <div class="col-7" id="modalIccid"></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-5 text-muted"><?php echo e(t('activation_code')); ?>:</div>
                                    <div class="col-7">
                                        <span id="modalActivationCode" class="text-monospace small"></span>
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-5 text-muted">PIN:</div>
                                    <div class="col-7" id="modalPin"></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-5 text-muted">PUK:</div>
                                    <div class="col-7" id="modalPuk"></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-5 text-muted">APN:</div>
                                    <div class="col-7" id="modalApn"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><?php echo e(t('package_details')); ?></h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="modalPackageList" class="list-group list-group-flush">
                                    <!-- Paket detayları buraya gelecek -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h6><?php echo e(t('activation_instructions')); ?></h6>
                    <ol class="ps-3">
                        <li><?php echo e(t('scan_qr_instruction')); ?></li>
                        <li><?php echo e(t('follow_device_prompts')); ?></li>
                        <li><?php echo e(t('enable_data_roaming')); ?></li>
                        <li><?php echo e(t('restart_device')); ?></li>
                    </ol>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(t('close')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Usage Modal -->
<div class="modal fade" id="usageModal" tabindex="-1" aria-labelledby="usageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="usageModalLabel"><?php echo e(t('data_usage_title')); ?></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="usageLoading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden"><?php echo e(t('loading')); ?>...</span>
                    </div>
                    <p class="mt-2"><?php echo e(t('loading_usage_data')); ?>...</p>
                </div>
                
                <div id="usageContent" style="display: none;">
                    <div class="usage-info mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title text-muted"><?php echo e(t('used_data_label')); ?></h6>
                                        <h4 class="card-text" id="usedData">0 MB</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title text-muted"><?php echo e(t('total_data_label')); ?></h6>
                                        <h4 class="card-text" id="totalData">0 GB</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card bg-light mb-3">
                            <div class="card-body">
                                <h6 class="card-title text-muted text-center"><?php echo e(t('usage_progress')); ?></h6>
                                <div class="progress" style="height: 25px;">
                                    <div id="usageProgressBar" class="progress-bar progress-bar-striped" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center text-muted">
                            <small id="lastUpdateTime"><?php echo e(t('last_updated_label')); ?>: --</small>
                        </div>
                    </div>
                </div>
                
                <div id="usageError" class="alert alert-danger" style="display: none;">
                    <?php echo e(t('usage_data_error')); ?>

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(t('close')); ?></button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function showEsimDetailsModal(esim) {
    console.log('Showing eSIM details in modal:', esim);
    
    // QR Code
    if (esim.qrCodeUrl) {
        document.getElementById('modalQrCodeImage').src = esim.qrCodeUrl;
        document.getElementById('qrCodeContainer').classList.remove('d-none');
        document.getElementById('downloadQrBtn').href = esim.qrCodeUrl;
    } else {
        document.getElementById('qrCodeContainer').classList.add('d-none');
    }
    
    // Temel bilgiler
    document.getElementById('modalOrderNo').textContent = esim.orderNo || 'N/A';
    
    // Durum
    const statusElement = document.getElementById('modalEsimStatus');
    let statusText = esim.esimStatus || 'UNKNOWN';
    let statusClass = 'bg-secondary';
    
    switch (statusText) {
        case 'AVAILABLE':
            statusClass = 'bg-success';
            statusText = '<?php echo e(t("status_available")); ?>';
            break;
        case 'ACTIVE':
            statusClass = 'bg-primary';
            statusText = '<?php echo e(t("status_active")); ?>';
            break;
        case 'EXPIRED':
            statusClass = 'bg-danger';
            statusText = '<?php echo e(t("status_expired")); ?>';
            break;
        case 'CANCEL':
            statusClass = 'bg-warning text-dark';
            statusText = '<?php echo e(t("status_cancelled")); ?>';
            break;
        default:
            statusText = statusText.charAt(0) + statusText.slice(1).toLowerCase();
            break;
    }
    
    statusElement.textContent = statusText;
    statusElement.className = 'badge ' + statusClass;
    
    // Diğer detaylar
    const expiredDate = esim.expiredTime ? new Date(esim.expiredTime).toLocaleDateString('<?php echo e(app()->getLocale()); ?>', { 
        year: 'numeric', month: 'long', day: 'numeric' 
    }) : 'N/A';
    document.getElementById('modalExpiredTime').textContent = expiredDate;
    
    document.getElementById('modalIccid').textContent = esim.iccid || 'N/A';
    document.getElementById('modalActivationCode').textContent = esim.ac || 'N/A';
    document.getElementById('modalPin').textContent = esim.pin || 'N/A';
    document.getElementById('modalPuk').textContent = esim.puk || 'N/A';
    document.getElementById('modalApn').textContent = esim.apn || 'N/A';
    
    // Paket listesi
    const packageListElement = document.getElementById('modalPackageList');
    packageListElement.innerHTML = '';
    
    if (esim.packageList && esim.packageList.length > 0) {
        esim.packageList.forEach(pkg => {
            const dataAmount = (pkg.volume / (1024 * 1024 * 1024)).toFixed(2);
            const createDate = pkg.createTime ? new Date(pkg.createTime).toLocaleDateString('<?php echo e(app()->getLocale()); ?>') : 'N/A';
            
            const packageItem = document.createElement('div');
            packageItem.className = 'list-group-item';
            packageItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0 fw-bold">${pkg.packageName || 'Unknown Package'}</h6>
                    <span class="badge bg-info">${pkg.locationCode || ''}</span>
                </div>
                <div class="row g-2 small">
                    <div class="col-sm-6">
                        <div class="text-muted"><?php echo e(t('data')); ?>:</div>
                        <div>${dataAmount} GB</div>
                    </div>
                    <div class="col-sm-6">
                        <div class="text-muted"><?php echo e(t('duration')); ?>:</div>
                        <div>${pkg.duration || 'N/A'} <?php echo e(t('days')); ?></div>
                    </div>
                    <div class="col-sm-6">
                        <div class="text-muted"><?php echo e(t('package_code')); ?>:</div>
                        <div>${pkg.packageCode || 'N/A'}</div>
                    </div>
                    <div class="col-sm-6">
                        <div class="text-muted"><?php echo e(t('created_on')); ?>:</div>
                        <div>${createDate}</div>
                    </div>
                </div>
            `;
            packageListElement.appendChild(packageItem);
        });
    } else {
        const noPackageItem = document.createElement('div');
        noPackageItem.className = 'list-group-item text-center text-muted';
        noPackageItem.textContent = '<?php echo e(t("no_package_info")); ?>';
        packageListElement.appendChild(noPackageItem);
    }
    
    // Aktivasyon kodu kopyalama butonu
    document.getElementById('copyActivationCodeBtn').addEventListener('click', function() {
        const activationCode = esim.ac || '';
        if (activationCode) {
            navigator.clipboard.writeText(activationCode).then(() => {
                // Başarılı kopyalama bildirimi
                this.innerHTML = '<i class="fas fa-check me-2"></i><?php echo e(t("copied")); ?>';
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-copy me-2"></i><?php echo e(t("copy_activation_code")); ?>';
                }, 2000);
            }).catch(err => {
                console.error('Kopyalama başarısız:', err);
                alert('<?php echo e(t("copy_failed")); ?>');
            });
        }
    });
    
    // Modal'ı göster
    const modal = new bootstrap.Modal(document.getElementById('esimDetailsModal'));
    modal.show();
}

// eSIM sorgulama fonksiyonu
function queryEsim(orderNo, showInModal = false) {
    console.log('Querying eSIM:', orderNo, 'Show in modal:', showInModal);
    
    // Yükleniyor göstergesi
    const loadingElement = document.createElement('div');
    loadingElement.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-dark bg-opacity-50';
    loadingElement.style.zIndex = '9999';
    loadingElement.innerHTML = `
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden"><?php echo e(t('loading')); ?>...</span>
        </div>
    `;
    document.body.appendChild(loadingElement);
    
    // Prepare request details
    const url = '/profile/query-esim';
    const method = 'POST';
    const headers = {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        'Accept': 'application/json'
    };
    const body = JSON.stringify({ orderNo: orderNo });
    
    console.log('API Request Details:');
    console.log('URL:', url);
    console.log('Method:', method);
    console.log('Headers:', headers);
    console.log('Body:', body);
    
    // Make the request
    fetch(url, {
        method: method,
        headers: headers,
        body: body
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        // Yükleniyor göstergesini kaldır
        document.body.removeChild(loadingElement);
        
        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return response.json();
        } else {
            throw new Error('Response is not JSON');
        }
    })
    .then(data => {
        console.log('API response:', data);
        
        if (data.success) {
            // Process successful response
            if (showInModal) {
                showEsimDetailsModal(data.data);
            } else {
                updateEsimDetails(data.data);
            }
        } else {
            // Handle error response
            Swal.fire({
                icon: 'error',
                title: '<?php echo e(t("error")); ?>',
                text: data.message || '<?php echo e(t("unknown_error")); ?>',
                confirmButtonText: '<?php echo e(t("ok")); ?>'
            });
        }
    })
    .catch(error => {
        // Yükleniyor göstergesini kaldır (hata durumunda)
        if (document.body.contains(loadingElement)) {
            document.body.removeChild(loadingElement);
        }
        
        console.error('API request failed:', error);
        Swal.fire({
            icon: 'error',
            title: '<?php echo e(t("error")); ?>',
            text: '<?php echo e(t("query_failed")); ?>',
            confirmButtonText: '<?php echo e(t("ok")); ?>'
        });
    });
}

// Query eSIM form submission
document.addEventListener('DOMContentLoaded', function() {
    // Query eSIM form submission
    const queryEsimForm = document.getElementById('queryEsimForm');
    if (queryEsimForm) {
        queryEsimForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const orderNo = document.getElementById('order_no').value;
            queryEsim(orderNo);
        });
    }
    
    // Query buttons in the table
    const queryButtons = document.querySelectorAll('.query-esim');
    queryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const orderNo = this.getAttribute('data-order-no');
            queryEsim(orderNo, true);
        });
    });
});

// E-posta gönderme işlemi
document.addEventListener('DOMContentLoaded', function() {
    const resendButtons = document.querySelectorAll('.resend-email');
    resendButtons.forEach(button => {
        button.addEventListener('click', function() {
            const orderNo = this.getAttribute('data-order-no');
            resendEsimEmail(orderNo);
        });
    });
});

function resendEsimEmail(orderNo) {
    // Yükleniyor göstergesi
    const loadingElement = document.createElement('div');
    loadingElement.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-dark bg-opacity-50';
    loadingElement.style.zIndex = '9999';
    loadingElement.innerHTML = `
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden"><?php echo e(t('loading')); ?>...</span>
        </div>
    `;
    document.body.appendChild(loadingElement);
    
    // API isteği
    fetch('/profile/resend-esim-email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
        },
        body: JSON.stringify({ orderNo: orderNo })
    })
    .then(response => response.json())
    .then(data => {
        // Yükleniyor göstergesini kaldır
        document.body.removeChild(loadingElement);
        
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: '<?php echo e(t("success")); ?>',
                text: data.message,
                confirmButtonText: '<?php echo e(t("ok")); ?>'
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: '<?php echo e(t("error")); ?>',
                text: data.message,
                confirmButtonText: '<?php echo e(t("ok")); ?>'
            });
        }
    })
    .catch(error => {
        // Yükleniyor göstergesini kaldır
        if (document.body.contains(loadingElement)) {
            document.body.removeChild(loadingElement);
        }
        
        console.error('API request failed:', error);
        Swal.fire({
            icon: 'error',
            title: '<?php echo e(t("error")); ?>',
            text: '<?php echo e(t("general_error")); ?>',
            confirmButtonText: '<?php echo e(t("ok")); ?>'
        });
    });
}

// Paket durumu kontrolü
document.addEventListener('DOMContentLoaded', function() {
    const packageStatusButtons = document.querySelectorAll('.check-package-status');
    packageStatusButtons.forEach(button => {
        button.addEventListener('click', function() {
            const orderNo = this.getAttribute('data-order-no');
            checkPackageStatus(orderNo);
        });
    });
});

function checkPackageStatus(orderNo) {
    // Yükleniyor göstergesi
    Swal.fire({
        title: 'Paket durumu kontrol ediliyor...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    // API isteği
    fetch(`/esim/package-status/${orderNo}`, {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Paket durumu bilgilerini göster
            Swal.fire({
                title: 'Paket Durumu',
                html: `
                    <div class="text-start">
                        <p><strong>Kullanılan Veri:</strong> ${data.remainingData}</p>
                        <p><strong>Toplam Veri:</strong> ${data.totalData}</p>
                        <p><strong>Kullanım Oranı:</strong> %${data.usagePercentage.toFixed(2)}</p>
                        <p><strong>Son Güncelleme:</strong> ${data.lastUpdateTime}</p>
                        <p><strong>Kalan Süre:</strong> ${data.remainingDays} gün</p>
                        <p><strong>Son Kullanma Tarihi:</strong> ${data.expiryDate}</p>
                    </div>
                `,
                icon: 'info'
            });
        } else {
            Swal.fire({
                title: 'Hata',
                text: data.message || 'Paket durumu alınamadı.',
                icon: 'error'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            title: 'Hata',
            text: 'Paket durumu kontrol edilirken bir hata oluştu.',
            icon: 'error'
        });
    });
}

// Kullanım durumu kontrolü
function showUsageModal(esimTranNo) {
    // Show modal
    const usageModal = new bootstrap.Modal(document.getElementById('usageModal'));
    usageModal.show();
    
    // Show loading indicator, hide content and error message
    document.getElementById('usageLoading').style.display = 'block';
    document.getElementById('usageContent').style.display = 'none';
    document.getElementById('usageError').style.display = 'none';
    
    // API request
    fetch(`/esim/usage/${esimTranNo}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        // Hide loading indicator
        document.getElementById('usageLoading').style.display = 'none';
        
        if (data.success) {
            // Show usage data
            document.getElementById('usageContent').style.display = 'block';
            
            // Fill in data - handle potential undefined or null values
            const usedData = data.used_data || data.dataUsage || data.usedData || 0;
            const totalData = data.total_data || data.totalData || data.packageData || 5 * 1024 * 1024 * 1024; // Default to 5GB if not provided
            const lastUpdated = data.last_updated || data.lastUpdateTime || data.lastUpdated || new Date().toISOString();
            
            document.getElementById('usedData').textContent = formatDataSize(usedData);
            document.getElementById('totalData').textContent = formatDataSize(totalData);
            
            try {
                const formattedDate = formatDateTime(lastUpdated);
                document.getElementById('lastUpdateTime').textContent = '<?php echo e(t("last_updated_label")); ?>: ' + formattedDate;
            } catch (e) {
                document.getElementById('lastUpdateTime').textContent = '<?php echo e(t("last_updated_label")); ?>: ' + new Date().toLocaleString();
            }
            
            // Update progress bar - handle potential NaN
            let percentage = calculatePercentage(usedData, totalData);
            if (isNaN(percentage)) percentage = 0;
            
            const progressBar = document.getElementById('usageProgressBar');
            progressBar.style.width = percentage + '%';
            progressBar.textContent = percentage.toFixed(1) + '%';
            
            // Set progress bar color based on usage
            if (percentage < 50) {
                progressBar.className = 'progress-bar progress-bar-striped bg-success';
            } else if (percentage < 80) {
                progressBar.className = 'progress-bar progress-bar-striped bg-warning';
            } else {
                progressBar.className = 'progress-bar progress-bar-striped bg-danger';
            }
        } else {
            // Show error message
            document.getElementById('usageError').style.display = 'block';
            console.error('Usage data error:', data.message);
        }
    })
    .catch(error => {
        // Hide loading indicator and show error message
        document.getElementById('usageLoading').style.display = 'none';
        document.getElementById('usageError').style.display = 'block';
        console.error('API request failed:', error);
    });
}

// Helper function to format data size
function formatDataSize(bytes) {
    if (!bytes || isNaN(bytes)) return '0 B';
    bytes = Number(bytes);
    
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 ' + sizes[0];
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    if (i >= sizes.length) return bytes + ' ' + sizes[sizes.length - 1];
    
    return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + sizes[i];
}

// Helper function to format date and time
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return 'Not available';
    
    try {
        const date = new Date(dateTimeStr);
        if (isNaN(date.getTime())) return 'Not available';
        return date.toLocaleString();
    } catch (e) {
        return 'Not available';
    }
}

// Helper function to calculate percentage
function calculatePercentage(used, total) {
    if (!used || !total || total === 0 || isNaN(used) || isNaN(total)) return 0;
    used = Number(used);
    total = Number(total);
    return Math.min(100, (used / total) * 100); // Cap at 100%
}

// Kullanım durumu butonlarına olay dinleyicisi ekle
document.addEventListener('DOMContentLoaded', function() {
    const usageButtons = document.querySelectorAll('.check-usage');
    usageButtons.forEach(button => {
        button.addEventListener('click', function() {
            const esimTranNo = this.getAttribute('data-esim-tran-no');
            showUsageModal(esimTranNo);
        });
    });
});

document.addEventListener('DOMContentLoaded', function() {
    // Sayfa yüklendiğinde tüm siparişlerin detaylarını çek
    const orderRows = document.querySelectorAll('tr[data-order-no]');
    orderRows.forEach(row => {
        const orderNo = row.getAttribute('data-order-no');
        fetchEsimDetails(orderNo, row);
    });
    
    // eSIM detaylarını çekme fonksiyonu
    function fetchEsimDetails(orderNo, rowElement) {
        // Yükleniyor göstergesi
        const statusCell = rowElement.querySelector('.status-cell');
        const expiresCell = rowElement.querySelector('.expires-cell');
        
        statusCell.innerHTML = '<span class="badge bg-secondary"><?php echo e(t("loading")); ?>...</span>';
        expiresCell.innerHTML = '<span class="text-muted"><?php echo e(t("loading")); ?>...</span>';
        
        fetch('/profile/query-esim', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            },
            body: JSON.stringify({ orderNo: orderNo })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Status bilgisini güncelle
                let statusText = data.data.esimStatus || 'UNKNOWN';
                let statusClass = 'bg-secondary';
                
                switch (statusText) {
                    case 'AVAILABLE':
                        statusClass = 'bg-success';
                        statusText = '<?php echo e(t("status_available")); ?>';
                        break;
                    case 'ACTIVE':
                        statusClass = 'bg-primary';
                        statusText = '<?php echo e(t("status_active")); ?>';
                        break;
                    case 'EXPIRED':
                        statusClass = 'bg-danger';
                        statusText = '<?php echo e(t("status_expired")); ?>';
                        break;
                    case 'CANCEL':
                        statusClass = 'bg-warning text-dark';
                        statusText = '<?php echo e(t("status_cancelled")); ?>';
                        break;
                    default:
                        statusText = statusText.charAt(0) + statusText.slice(1).toLowerCase();
                        break;
                }
                
                statusCell.innerHTML = `<span class="badge ${statusClass}">${statusText}</span>`;
                
                // Expires On bilgisini güncelle
                if (data.data.expiredTime) {
                    const expiredDate = new Date(data.data.expiredTime);
                    const today = new Date();
                    
                    // Kalan gün sayısını hesapla
                    const diffTime = expiredDate - today;
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    
                    // Tarih formatını ayarla
                    const formattedDate = expiredDate.toLocaleDateString('<?php echo e(app()->getLocale()); ?>', { 
                        year: 'numeric', month: 'long', day: 'numeric' 
                    });
                    
                    // Kalan gün sayısına göre renk ve metin belirle
                    let expiresText = formattedDate;
                    let expiresClass = 'text-success';
                    
                    if (diffDays < 0) {
                        expiresText = `<div class="d-flex align-items-center">
                            <div class="me-2">${formattedDate}</div>
                            <span class="badge bg-danger rounded-pill"><?php echo e(t("expired")); ?></span>
                        </div>`;
                        expiresClass = 'text-muted';
                    } else if (diffDays === 0) {
                        expiresText = `<div class="d-flex align-items-center">
                            <div class="me-2">${formattedDate}</div>
                            <span class="badge bg-warning text-dark rounded-pill"><?php echo e(t("today")); ?></span>
                        </div>`;
                        expiresClass = 'text-warning';
                    } else if (diffDays <= 3) {
                        expiresText = `<div class="d-flex align-items-center">
                            <div class="me-2">${formattedDate}</div>
                            <span class="badge bg-warning text-dark rounded-pill">${diffDays} <?php echo e(t("days")); ?></span>
                        </div>`;
                        expiresClass = 'text-warning';
                    } else {
                        expiresText = `<div class="d-flex align-items-center">
                            <div class="me-2">${formattedDate}</div>
                            <span class="badge bg-info rounded-pill">${diffDays} <?php echo e(t("days")); ?></span>
                        </div>`;
                    }
                    
                    expiresCell.innerHTML = `<span class="${expiresClass}">${expiresText}</span>`;
                } else {
                    expiresCell.innerHTML = '<span class="text-muted"><?php echo e(t("not_available")); ?></span>';
                }
                
                // eSIM transaction numarasını güncelle
                if (data.data.esimTranNo) {
                    const actionsCell = rowElement.querySelector('.actions-cell');
                    if (!actionsCell.querySelector('.check-usage')) {
                        const usageBtn = document.createElement('button');
                        usageBtn.className = 'btn btn-success btn-sm check-usage';
                        usageBtn.setAttribute('data-esim-tran-no', data.data.esimTranNo);
                        usageBtn.innerHTML = '<i class="fas fa-chart-pie"></i> <?php echo e(t("usage")); ?>';
                        usageBtn.addEventListener('click', function() {
                            showUsageModal(data.data.esimTranNo);
                        });
                        actionsCell.querySelector('.d-flex').appendChild(usageBtn);
                    }
                }
            }
        })
        .catch(error => {
            console.error('API request failed:', error);
            statusCell.innerHTML = '<span class="badge bg-danger"><?php echo e(t("error")); ?></span>';
            expiresCell.innerHTML = '<span class="text-danger"><?php echo e(t("error")); ?></span>';
        });
    }
});
</script>
<?php $__env->stopPush(); ?>








<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\vox-esim-web\resources\views/profile/dashboard.blade.php ENDPATH**/ ?>