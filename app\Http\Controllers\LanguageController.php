<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    public function switchLang(Request $request, $lang)
    {
        // Force English only for now
        Session::put('locale', 'en');
        app()->setLocale('en');
        
        return redirect()->back();
        
        /* Original code commented out
        // Desteklenen dilleri kontrol et
        if (in_array($lang, ['en', 'tr', 'de', 'es', 'fr', 'it', 'ja', 'ko', 'zh', 'ru', 'pt', 'ar'])) {
            Session::put('locale', $lang);
            app()->setLocale($lang);
        }
        return redirect()->back();
        */
    }
}


