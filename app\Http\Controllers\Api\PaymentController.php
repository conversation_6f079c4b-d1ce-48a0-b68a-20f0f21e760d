<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Services\RapydService;
use App\Models\Payment;

class PaymentController extends Controller
{
    private $headers;
    private $apiUrl;
    private $rapydService;

    public function __construct(RapydService $rapydService)
    {
        $this->apiUrl = config('services.esim.api_url');
        $this->headers = [
            'RT-AccessCode' => config('services.esim.access_code'),
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
        $this->rapydService = $rapydService;
    }

    /**
     * Create a Rapyd payment link
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createPaymentLink(Request $request)
    {
        try {
            // Validate request
            $validated = $request->validate([
                'package_code' => 'required|string',
                'name' => 'required|string',
                'email' => 'required|email',
                'phone' => 'nullable|string',
                'callback_url' => 'nullable|url',
                'price' => 'nullable|numeric'
            ]);
    
            $packageCode = $validated['package_code'];
            $name = $validated['name'];
            $email = $validated['email'];
            $phone = $validated['phone'] ?? '';
            $callbackUrl = route('payment.success'); // Her zaman payment.success
    
            $clientPrice = isset($validated['price']) ? $validated['price'] / 100 : null;
    
            // Get package details
            $packageResponse = Http::withHeaders($this->headers)
                ->post("{$this->apiUrl}/api/v1/open/package/list", [
                    'packageCode' => $packageCode
                ]);
    
            if (!$packageResponse->successful()) {
                Log::error('API - Package details fetch failed:', [
                    'status' => $packageResponse->status(),
                    'response' => $packageResponse->json(),
                    'package_code' => $packageCode
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Package information could not be retrieved.',
                    'error' => [
                        'status' => 'error',
                        'message' => 'API request failed'
                    ]
                ], 400);
            }
    
            $packageData = collect($packageResponse->json()['obj']['packageList'] ?? [])
                ->firstWhere('packageCode', $packageCode);
    
            if (!$packageData) {
                Log::error('API - Package not found:', [
                    'package_code' => $packageCode,
                    'available_packages' => $packageResponse->json()['obj']['packageList'] ?? []
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Package not found',
                    'error' => [
                        'status' => 'error',
                        'message' => 'Invalid package code'
                    ]
                ], 400);
            }
    
            // Calculate final price
            $finalPrice = $clientPrice ?? $this->calculateFinalPrice($packageData['price']);
    
            // Format data values
            $dataInGB = isset($packageData['volume'])
                ? round($packageData['volume'] / (1024 * 1024 * 1024), 2)
                : 'N/A';
    
            // Create a unique merchant reference ID
            $merchantReferenceId = 'ESIM_' . uniqid();
    
            // Create Rapyd customer
            $customerData = [
                'name' => $name,
                'email' => $email,
                'phone_number' => $phone ?: null
            ];
            $this->removeNullValues($customerData);
    
            $customerResponse = $this->rapydService->createCustomer($customerData['name'], $customerData['email']);
    
            if ($customerResponse === null || !$customerResponse->successful()) {
                Log::error('API - Customer creation failed:', [
                    'response' => $customerResponse ? $customerResponse->json() : null
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Customer could not be created.',
                    'error' => $customerResponse ? $customerResponse->json() : 'No response'
                ], 400);
            }
    
            $customerId = $customerResponse->json()['data']['id'];
    
            // Prepare checkout data
            $checkoutData = [
                'amount' => $finalPrice,
                'currency' => 'EUR',
                'country' => 'DE',
                'complete_payment_url' => $callbackUrl . '?payment_id={payment_id}&merchant_reference_id=' . $merchantReferenceId,
                'error_payment_url' => $callbackUrl . '?status=error&merchant_reference_id=' . $merchantReferenceId,
                'complete_checkout_url' => $callbackUrl . '?checkout_id={id}&merchant_reference_id=' . $merchantReferenceId,
                'cancel_checkout_url' => $callbackUrl . '?status=cancel&merchant_reference_id=' . $merchantReferenceId,
                'merchant_reference_id' => $merchantReferenceId,
                'language' => 'en',
                'metadata' => [
                    'package_code' => $packageCode,
                    'customer_email' => $email,
                    'customer_name' => $name
                ],
                'payment_method_types_include' => ['de_visa_card', 'de_mastercard_card'],
                'customer' => $customerId,
                'cart_items' => [
                    [
                        'name' => $packageData['name'],
                        'amount' => $finalPrice,
                        'quantity' => 1,
                        'image' => asset('images/logo.png'),
                        'description' => "Data: {$dataInGB} GB, Validity: {$packageData['duration']} days"
                    ]
                ]
            ];
    
            // Remove null values
            $this->removeNullValues($checkoutData);
    
            // Log the checkout request
            Log::info('API - Rapyd checkout request:', [
                'data' => $checkoutData
            ]);
    
            // Create checkout
            $response = $this->rapydService->createCheckout($checkoutData);
    
            // Log the Rapyd API response
            Log::info('API - Rapyd API response:', [
                'status' => $response->status(),
                'body' => $response->json(),
            ]);
    
            if ($response->successful()) {
                $checkoutData = $response->json()['data'];
    
                if (!isset($checkoutData['id']) || !isset($checkoutData['redirect_url'])) {
                    Log::error('API - Checkout data missing required fields:', [
                        'response' => $checkoutData,
                        'required_fields' => ['id', 'redirect_url'],
                        'available_fields' => array_keys($checkoutData)
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Payment information is incomplete.',
                        'error' => $checkoutData
                    ], 400);
                }
    
                // Save payment information to database
                $payment = Payment::create([
                    'payment_id' => $checkoutData['id'],
                    'status' => 'pending',
                    'merchant_reference_id' => $merchantReferenceId,
                    'order_id' => $merchantReferenceId,
                    'package_code' => $packageCode,
                    'email' => $email,
                    'name' => $name,
                    'payment_method' => 'rapyd',
                    'price_amount' => $finalPrice,
                    'price_currency' => 'EUR',
                    'rapyd_checkout_id' => $checkoutData['id']
                ]);
    
                // Return success response with payment link
                return response()->json([
                    'success' => true,
                    'message' => 'Payment link created successfully.',
                    'data' => [
                        'payment_id' => $checkoutData['id'],
                        'merchant_reference_id' => $merchantReferenceId,
                        'payment_url' => $checkoutData['redirect_url'],
                        'package' => [
                            'code' => $packageCode,
                            'name' => $packageData['name'],
                            'data' => $dataInGB . ' GB',
                            'validity' => $packageData['duration'] . ' days',
                            'price' => $finalPrice,
                            'currency' => 'EUR'
                        ]
                    ]
                ], 200);
            } else {
                Log::error('API - Checkout creation failed:', [
                    'status' => $response->status(),
                    'response' => $response->json(),
                    'error_message' => $response->json()['message'] ?? 'Unknown error'
                ]);
                $errorMessage = $response->json()['message'] ?? 'Unknown error';
                return response()->json([
                    'success' => false,
                    'message' => 'Payment could not be created: ' . $errorMessage,
                    'error' => $response->json()
                ], 400);
            }
        } catch (\Exception $e) {
            Log::error('API - Payment link creation error:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Verify payment status
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyPayment(Request $request)
    {
        try {
            $validated = $request->validate([
                'merchant_reference_id' => 'required|string',
            ]);

            $merchantReferenceId = $validated['merchant_reference_id'];

            // Find payment in database
            $payment = Payment::where('merchant_reference_id', $merchantReferenceId)
                ->orWhere('order_id', $merchantReferenceId)
                ->first();

            if (!$payment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment not found.',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'payment_id' => $payment->payment_id,
                    'merchant_reference_id' => $payment->merchant_reference_id,
                    'status' => $payment->status,
                    'package_code' => $payment->package_code,
                    'email' => $payment->email,
                    'name' => $payment->name,
                    'payment_method' => $payment->payment_method,
                    'price_amount' => $payment->price_amount,
                    'price_currency' => $payment->price_currency,
                    'created_at' => $payment->created_at,
                    'updated_at' => $payment->updated_at
                ]
            ], 200);
        } catch (\Exception $e) {
            Log::error('API - Payment verification error:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate final price with any applicable discounts or fees
     *
     * @param float $originalPrice
     * @return float
     */
    private function calculateFinalPrice($originalPrice)
    {
        // API'den gelen fiyatı USD'ye çevir (10000'e böl)
        $priceInUSD = $originalPrice / 10000;

        // %35 komisyon ekle
        $priceWithCommission = $priceInUSD * 1.35;

        // Fiyat 2$ ve altındaysa 1.99'a yuvarla
        if ($priceWithCommission <= 2) {
            return 1.99;
        }

        // 2$'dan büyük fiyatları önce 0.50'ye yuvarla
        $roundedPrice = ceil($priceWithCommission * 2) / 2;
        
        // Eğer .00 ile bitiyorsa .99'a çevir
        if (round($roundedPrice * 100) % 100 == 0) {
            return $roundedPrice - 0.01;
        }
        
        // Eğer .50 ile bitiyorsa .49'a çevir
        if (round($roundedPrice * 100) % 100 == 50) {
            return $roundedPrice - 0.01;
        }
        
        return $roundedPrice;
    }

    /**
     * Recursively remove null values from an array
     *
     * @param array &$data The array to process
     * @return void
     */
    private function removeNullValues(&$data)
    {
        foreach ($data as $key => &$value) {
            if (is_null($value)) {
                unset($data[$key]);
            } elseif (is_array($value)) {
                $this->removeNullValues($value);

                // If the array is now empty, remove it
                if (empty($value)) {
                    unset($data[$key]);
                }
            }
        }
    }
}

