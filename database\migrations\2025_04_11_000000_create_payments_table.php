<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('payments')) {
            Schema::create('payments', function (Blueprint $table) {
                $table->id();
                $table->string('payment_id')->nullable();
                $table->string('status')->nullable();
                $table->string('pay_address')->nullable();
                $table->decimal('pay_amount', 18, 8)->nullable();
                $table->string('pay_currency')->nullable();
                $table->decimal('price_amount', 10, 2)->nullable();
                $table->string('price_currency')->nullable();
                $table->string('order_id')->nullable();
                $table->string('package_code')->nullable();
                $table->string('email')->nullable();
                $table->string('name')->nullable();
                $table->string('network')->nullable();
                $table->string('payment_method')->default('card');
                $table->timestamp('expiration_estimate_date')->nullable();
                $table->timestamps();
            });
        } else {
            // Add payment_method column if it doesn't exist
            if (!Schema::hasColumn('payments', 'payment_method')) {
                Schema::table('payments', function (Blueprint $table) {
                    $table->string('payment_method')->default('card')->after('network');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to drop the table if it already existed
        if (Schema::hasColumn('payments', 'payment_method')) {
            Schema::table('payments', function (Blueprint $table) {
                $table->dropColumn('payment_method');
            });
        }
    }
};
