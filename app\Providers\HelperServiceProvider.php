<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class HelperServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Include the helpers file manually
        require_once app_path('Helpers/helpers.php');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register the translation helper
        $this->app->singleton('translation', function ($app) {
            return new \App\Helpers\TranslationHelper();
        });

        // Fallback definition for t() function if it doesn't exist
        if (!function_exists('t')) {
            function t($key = null, $replace = [], $locale = null) {
                return \App\Helpers\TranslationHelper::trans($key, $replace, $locale);
            }
        }
    }
}
