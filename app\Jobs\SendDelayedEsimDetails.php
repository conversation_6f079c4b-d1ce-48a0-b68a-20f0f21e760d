<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use App\Mail\EsimPurchaseConfirmation;

class SendDelayedEsimDetails implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $orderNo;
    protected $email;
    public $tries = 5; // Maksimum deneme sayısı
    public $backoff = 10; // Denemeler arası beklenecek süre (saniye)

    public function __construct($orderNo, $email)
    {
        $this->orderNo = $orderNo;
        $this->email = $email;
    }

    public function handle()
    {
        $headers = [
            'RT-AccessCode' => config('services.esim.access_code')
        ];
        
        $detailsResponse = Http::withHeaders($headers)
            ->post(config('services.esim.api_url') . '/api/v1/open/esim/query', [
                'orderNo' => $this->orderNo,
                'pager' => ['page' => 1, 'pageSize' => 10]
            ]);

        if (!$detailsResponse->successful()) {
            throw new \Exception('eSIM detayları alınamadı');
        }

        $esimDetails = $detailsResponse->json()['obj']['esimList'][0] ?? null;
        
        if (!$esimDetails || 
            empty($esimDetails['iccid']) || 
            empty($esimDetails['ac']) ||
            empty($esimDetails['pin'])) {
            throw new \Exception('eSIM detayları henüz hazır değil');
        }

        // QR kod görüntüsünü indir
        $qrCodeImage = null;
        if (!empty($esimDetails['qrCodeUrl'])) {
            $qrResponse = Http::get($esimDetails['qrCodeUrl']);
            if ($qrResponse->successful()) {
                $qrCodeImage = $qrResponse->body();
            }
        }

        $packageInfo = $esimDetails['packageList'][0] ?? [];
        
        $orderDetails = [
            'orderNo' => $esimDetails['orderNo'],
            'iccid' => $esimDetails['iccid'],
            'activationCode' => $esimDetails['ac'],
            'qrCodeImage' => $qrCodeImage,
            'qrCodeUrl' => $esimDetails['qrCodeUrl'],
            'pin' => $esimDetails['pin'],
            'puk' => $esimDetails['puk'],
            'apn' => $esimDetails['apn'],
            'expiredTime' => $esimDetails['expiredTime'],
            'packageName' => $packageInfo['packageName'] ?? '',
            'data' => isset($packageInfo['volume']) ? round($packageInfo['volume'] / (1024 * 1024 * 1024), 2) . ' GB' : '',
            'validity' => $packageInfo['duration'] ?? '',
            'customerName' => 'Değerli Müşterimiz' // Varsayılan isim
        ];

        Mail::to($this->email)->send(new EsimPurchaseConfirmation($orderDetails));
    }
}