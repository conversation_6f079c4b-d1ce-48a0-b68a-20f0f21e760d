document.addEventListener('DOMContentLoaded', function() {
    // Çerez popup'ını göster
    function showCookieConsent() {
        const cookieConsent = document.getElementById('cookie-consent');
        if (cookieConsent) {
            setTimeout(() => {
                cookieConsent.classList.add('show');
            }, 1500);
        }
    }
    
    // Çerez tercihini kaydet
    function setCookieConsent(accepted) {
        const expiryDate = new Date();
        expiryDate.setMonth(expiryDate.getMonth() + 12); // 12 ay geçerli
        
        document.cookie = `cookie_consent=${accepted ? 'accepted' : 'declined'};expires=${expiryDate.toUTCString()};path=/;SameSite=Lax`;
        
        // Google Analytics'i etkinleştir/devre dışı bırak
        if (typeof gtag === 'function') {
            gtag('consent', 'update', {
                'analytics_storage': accepted ? 'granted' : 'denied',
                'ad_storage': accepted ? 'granted' : 'denied'
            });
        }
        
        // Popup'ı gizle
        const cookieConsent = document.getElementById('cookie-consent');
        if (cookieConsent) {
            cookieConsent.classList.remove('show');
            setTimeout(() => {
                cookieConsent.style.display = 'none';
            }, 400);
        }
    }
    
    // Çerez tercihini kontrol et
    function checkCookieConsent() {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('cookie_consent=')) {
                return cookie.substring('cookie_consent='.length, cookie.length);
            }
        }
        return null;
    }
    
    // Buton event listener'ları
    document.addEventListener('click', function(e) {
        if (e.target.id === 'accept-cookies') {
            setCookieConsent(true);
        } else if (e.target.id === 'decline-cookies') {
            setCookieConsent(false);
        }
    });
    
    // Çerez tercihi yoksa popup'ı göster
    const consentValue = checkCookieConsent();
    if (!consentValue) {
        showCookieConsent();
    } else if (typeof gtag === 'function') {
        // Önceki tercihi Google Analytics'e bildir
        gtag('consent', 'update', {
            'analytics_storage': consentValue === 'accepted' ? 'granted' : 'denied',
            'ad_storage': consentValue === 'accepted' ? 'granted' : 'denied'
        });
    }
});
