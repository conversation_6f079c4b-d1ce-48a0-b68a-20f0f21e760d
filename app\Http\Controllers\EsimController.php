<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Carbon\Carbon;
use \Stripe\Stripe as StripeGateway;
use \Stripe\PaymentIntent;
use \Stripe\Exception\ApiErrorException;
use Illuminate\Support\Facades\Mail;
use App\Mail\EsimPurchaseConfirmation;
use App\Services\RapydService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\EsimService;

class EsimController extends Controller
{
    private $apiUrl;
    private $headers;
    private $nowPaymentsApiUrl;
    private $rapydService;

    public function __construct(RapydService $rapydService)
    {
        $this->apiUrl = config('services.esim.api_url');
        $this->headers = [
            'RT-AccessCode' => config('services.esim.access_code'),
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ];
        $this->nowPaymentsApiUrl = config('services.nowpayments.api_url');
        $this->rapydService = $rapydService;
    }

    public function index()
    {
        $response = Http::withHeaders($this->headers)
            ->post("{$this->apiUrl}/api/v1/open/location/list", (object)[]);

        $data = $response->json();

        if ($response->successful() && isset($data['obj']['locationList'])) {
            $locations = $data['obj']['locationList'];
        } else {
            $locations = [];
        }

        return view('home', compact('locations'));
    }

    private function calculateFinalPrice($originalPrice)
    {
        // API'den gelen fiyatı USD'ye çevir (10000'e böl)
        $priceInUSD = $originalPrice / 10000;

        // %35 komisyon ekle
        $priceWithCommission = $priceInUSD * 1.35;

        // Fiyat 2$ ve altındaysa 1.99'a yuvarla
        if ($priceWithCommission <= 2) {
            return 1.99;
        }

        // 2$'dan büyük fiyatları önce 0.50'ye yuvarla
        $roundedPrice = ceil($priceWithCommission * 2) / 2;
        
        // Eğer .00 ile bitiyorsa .99'a çevir
        if (round($roundedPrice * 100) % 100 == 0) {
            return $roundedPrice - 0.01;
        }
        
        // Eğer .50 ile bitiyorsa .49'a çevir
        if (round($roundedPrice * 100) % 100 == 50) {
            return $roundedPrice - 0.01;
        }
        
        return $roundedPrice;
    }

    public function showCountry($code)
    {
        $code = strtoupper($code);

        $locations = $this->getLocations();

        $location = collect($locations)->firstWhere('code', $code);

        if (!$location) {
            \Log::warning('Location not found:', ['code' => $code]);
            return redirect()->route('home')->with('error', 'Belirtilen ülke bulunamadı.');
        }

        // API'ye doğru formatta istek atalım
        $response = Http::withHeaders($this->headers)
            ->post("{$this->apiUrl}/api/v1/open/package/list", [
                'locationCode' => ''
            ]);

        $countryPackages = []; // Sadece ülke slug'ına göre paketler
        $allValidPackages = []; // Ülkede geçerli tüm paketler

        if ($response->successful() && isset($response->json()['obj']['packageList'])) {
            $allPackages = $response->json()['obj']['packageList'];
            
            // Paketleri gruplandırmak için geçici diziler
            $countryPackageGroups = [];
            $allValidPackageGroups = [];
            
            foreach ($allPackages as $pkg) {
                $price = $pkg['price'] ?? 0;
                $priceInDollars = $price / 10000;

                if ($priceInDollars < 1) continue;

                $finalPrice = $this->calculateFinalPrice($price);
                $dataInGB = isset($pkg['volume']) ? round($pkg['volume'] / (1024 * 1024 * 1024), 2) : 0;
                $validity = $pkg['duration'] ?? 0;
                
                // GB başına fiyat hesapla
                $pricePerGB = $dataInGB > 0 ? round($finalPrice / $dataInGB, 2) : 0;
                // Dolar başına GB hesapla
                $gbPerDollar = $finalPrice > 0 ? round($dataInGB / $finalPrice, 2) : 0;

                // Lokasyon kodlarını ülke adlarına çevirelim
                $locationCodes = explode(',', $pkg['location'] ?? '');
                $locationCodes = array_map('trim', $locationCodes);
                
                $locationNames = [];
                $locationCodesForFlags = []; // Bayraklar için ülke kodları

                foreach ($locationCodes as $locCode) {
                    $loc = collect($locations)->firstWhere('code', $locCode);
                    if ($loc) {
                        $locationNames[] = $loc['name'];
                        $locationCodesForFlags[] = strtolower($locCode); // Bayrak için küçük harfli kod
                    } else {
                        $locationNames[] = $locCode; // Eğer isim bulunamazsa kodu kullan
                        $locationCodesForFlags[] = strtolower($locCode);
                    }
                }

                $packageData = [
                    'name' => $pkg['name'] ?? 'Bilinmeyen',
                    'validity' => $validity,
                    'data' => $dataInGB . ' GB',
                    'data_amount' => $dataInGB, // Sıralama için ham veri değeri
                    'price' => number_format($finalPrice, 2),
                    'price_raw' => $finalPrice, // Sıralama için ham fiyat değeri
                    'price_per_gb' => $pricePerGB, // GB başına fiyat
                    'gb_per_dollar' => $gbPerDollar, // Dolar başına GB
                    'packageCode' => $pkg['packageCode'] ?? '',
                    'locationCodes' => $locationCodes,
                    'locationNames' => $locationNames,
                    'locationCodesForFlags' => $locationCodesForFlags, // Bayraklar için ülke kodları
                    'speed' => $pkg['speed'] ?? 'Premium Quality', // Ağ hızı
                ];
                
                // Ülkede geçerli tüm paketleri kontrol et
                $isValidInCountry = false;
                if (in_array($code, $locationCodes)) {
                    $isValidInCountry = true;
                    
                    // Paketleri GB ve fiyata göre gruplandır
                    $groupKey = $dataInGB . '_' . $finalPrice;
                    if (!isset($allValidPackageGroups[$groupKey])) {
                        $allValidPackageGroups[$groupKey] = [];
                    }
                    $allValidPackageGroups[$groupKey][] = $packageData;
                }
                
                // Slug'ın başında ülke kodu olanları kontrol et
                $slug = $pkg['slug'] ?? '';
                $slugParts = explode('_', $slug);
                $slugCountry = $slugParts[0] ?? '';
                
                if ($slugCountry === $code) {
                    // Paketleri GB ve fiyata göre gruplandır
                    $groupKey = $dataInGB . '_' . $finalPrice;
                    if (!isset($countryPackageGroups[$groupKey])) {
                        $countryPackageGroups[$groupKey] = [];
                    }
                    $countryPackageGroups[$groupKey][] = $packageData;
                }
            }
            
            // Her grup için en uzun geçerlilik süresine sahip paketi seç
            foreach ($countryPackageGroups as $packages) {
                usort($packages, function($a, $b) {
                    return $b['validity'] <=> $a['validity'];
                });
                $countryPackages[] = $packages[0];
            }
            
            // Tüm geçerli paketler için de aynı işlemi yap
            foreach ($allValidPackageGroups as $packages) {
                usort($packages, function($a, $b) {
                    return $b['validity'] <=> $a['validity'];
                });
                $allValidPackages[] = $packages[0];
            }
            
            // Paketleri fiyata göre sıralayalım (ucuzdan pahalıya)
            usort($countryPackages, function($a, $b) {
                return $a['price_raw'] <=> $b['price_raw'];
            });
            
            usort($allValidPackages, function($a, $b) {
                return $a['price_raw'] <=> $b['price_raw'];
            });
        } else {
            // API hatası durumunda loglayalım
            \Log::error('API Error:', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
        }

        \Log::info('Packages found:', [
            'country_code' => $code,
            'country_packages' => count($countryPackages),
            'all_valid_packages' => count($allValidPackages)
        ]);

        return view('country', [
            'code' => $code,
            'location' => $location,
            'countryPackages' => $countryPackages,
            'allValidPackages' => $allValidPackages
        ]);
    }

    private function getLocations()
    {
        return Cache::remember('esim_locations', 3600, function () {
            $response = Http::withHeaders($this->headers)
                ->post("{$this->apiUrl}/api/v1/open/location/list", (object)[]);

            return $response->successful()
                ? ($response->json()['obj']['locationList'] ?? [])
                : [];
        });
    }

    public function checkout($packageCode)
    {
        try {
            $packageResponse = Http::withHeaders($this->headers)
                ->post("{$this->apiUrl}/api/v1/open/package/list", [
                    'packageCode' => $packageCode,
                ]);

            if (!$packageResponse->successful()) {
                return redirect()->back()->with('error', 'Paket bilgileri alınamadı.');
            }

            $packageData = collect($packageResponse->json()['obj']['packageList'])
                ->firstWhere('packageCode', $packageCode);

            if (!$packageData) {
                return redirect()->back()->with('error', 'Paket bulunamadı.');
            }

            $finalPrice = $this->calculateFinalPrice($packageData['price']);

            $package = [
                'name' => $packageData['name'] ?? 'Bilinmeyen',
                'validity' => $packageData['duration'] ?? 'Bilinmiyor',
                'data' => isset($packageData['volume']) ? round($packageData['volume'] / (1024 * 1024 * 1024), 2) . ' GB' : 'Belirtilmemiş',
                'price' => number_format($finalPrice, 2),
                'packageCode' => $packageData['packageCode'] ?? '',
            ];

            // Kripto para birimleri için minimum tutarları al
            $currencies = ['btc', 'eth', 'usdt', 'usdc'];
            $currencyMinAmounts = [];
            $currencyErrors = [];

            foreach ($currencies as $currency) {
                try {
                    $minAmountResponse = Http::withHeaders([
                        'x-api-key' => config('services.nowpayments.api_key'),
                    ])->get("{$this->nowPaymentsApiUrl}/min-amount", [
                        'currency_from' => $currency,
                        'currency_to' => 'usd',
                    ]);

                    if ($minAmountResponse->successful()) {
                        $currencyMinAmounts[$currency] = $minAmountResponse->json()['min_amount'] ?? PHP_FLOAT_MAX;
                        $currencyErrors[$currency] = '';
                    } else {
                        $currencyMinAmounts[$currency] = PHP_FLOAT_MAX;
                        $currencyErrors[$currency] = 'Minimum tutar alınamadı';
                    }
                } catch (\Exception $e) {
                    $currencyMinAmounts[$currency] = PHP_FLOAT_MAX;
                    $currencyErrors[$currency] = 'Bağlantı hatası';
                }
            }

            return view('esim.checkout', [
                'package' => $package,
                'currencies' => $currencies,
                'currencyMinAmounts' => $currencyMinAmounts,
                'currencyErrors' => $currencyErrors
            ]);

        } catch (\Exception $e) {
            \Log::error('Checkout error:', ['error' => $e->getMessage()]);
            return redirect()->back()->with('error', 'Bir hata oluştu.');
        }
    }

    public function purchase(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'email' => 'required|email',
            'payment_method' => 'required|in:card,crypto,rapyd',
            'package_code' => 'required',
            'pay_currency' => 'required_if:payment_method,crypto'
        ]);

        if ($request->payment_method === 'card') {
            return $this->processCardPayment($request);
        } elseif ($request->payment_method === 'rapyd') {
            return $this->processRapydPayment($request);
        } else {
            return $this->processCryptoPayment($request);
        }
    }

    private function processCardPayment($request)
    {
        try {
            $packageCode = $request->input('package_code');
            $email = $request->input('email');
            $name = $request->input('name');
    
            $packageResponse = Http::withHeaders($this->headers)
                ->post("{$this->apiUrl}/api/v1/open/package/list", [
                    'packageCode' => $packageCode,
                ]);
    
            if (!$packageResponse->successful()) {
                return redirect()->back()->with('error', 'Paket bilgileri alınamadı.');
            }
    
            $packageData = collect($packageResponse->json()['obj']['packageList'])
                ->firstWhere('packageCode', $packageCode);
    
            $finalPrice = $this->calculateFinalPrice($packageData['price']);
    
            // Stripe için tutarı cent'e çevir
            $amountInCents = (int)($finalPrice * 100);
    
            // Stripe ayarları
            StripeGateway::setApiKey(config('services.stripe.secret'));
    
            // Payment Intent oluştur
            $paymentIntent = PaymentIntent::create([
                'amount' => $amountInCents,
                'currency' => 'usd',
                'metadata' => [
                    'package_code' => $packageCode,
                    'customer_email' => $email,
                    'customer_name' => $name
                ]
            ]);
    
            // Payment kaydını hemen oluştur (status: pending)
            $payment = \App\Models\Payment::create([
                'payment_id' => $paymentIntent->id,
                'status' => 'pending', // Ödeme henüz tamamlanmadı
                'merchant_reference_id' => 'STRIPE_' . uniqid(),
                'order_id' => 'STRIPE_' . uniqid(),
                'package_code' => $packageCode,
                'email' => $email,
                'name' => $name,
                'payment_method' => 'card',
                'price_amount' => $finalPrice,
                'price_currency' => 'usd',
                'rapyd_checkout_id' => null
            ]);
    
            \Log::info('Stripe Payment record created:', [
                'payment_id' => $payment->payment_id,
                'status' => $payment->status,
                'email' => $email
            ]);
    
            return view('esim.card-payment', [
                'clientSecret' => $paymentIntent->client_secret,
                'packageData' => $packageData,
                'amount' => $finalPrice,
                'stripeKey' => config('services.stripe.key'),
                'customerName' => $name,
                'customerEmail' => $email
            ]);
    
        } catch (\Exception $e) {
            \Log::error('Card payment error:', ['error' => $e->getMessage()]);
            return redirect()->back()->with('error', 'Ödeme işlemi başlatılırken bir hata oluştu.');
        }
    }

    private function processRapydPayment($request)
    {
        try {
            $packageCode = $request->input('package_code');
            $email = $request->input('email');
            $name = $request->input('name');
            $phone = $request->input('phone', '');

            $packageResponse = Http::withHeaders($this->headers)
                ->post("{$this->apiUrl}/api/v1/open/package/list", [
                    'packageCode' => $packageCode,
                ]);

            if (!$packageResponse->successful()) {
                \Log::error('Package fetch failed for payment:', [
                    'packageCode' => $packageCode,
                    'status' => $packageResponse->status(),
                    'response' => $packageResponse->json()
                ]);
                return redirect()->back()->with('error', 'Package information could not be retrieved.');
            }

            $packageData = collect($packageResponse->json()['obj']['packageList'])->firstWhere('packageCode', $packageCode);
            $finalPrice = $this->calculateFinalPrice($packageData['price']);

            // Create a unique merchant reference ID
            $merchantReferenceId = 'ESIM_' . uniqid();

            // Rapyd müşteri oluştur
            $customerData = [
                'name' => $name,
                'email' => $email,
                'phone_number' => $phone ?: null
            ];
            $this->removeNullValues($customerData);

            // createCustomer metodunu iki parametre ile çağırıyoruz
            $customerResponse = $this->rapydService->createCustomer($customerData['name'], $customerData['email']);

            // null kontrolü ekleyerek successful() hatasını önlüyoruz
            if ($customerResponse === null || !$customerResponse->successful()) {
                \Log::error('Customer creation failed:', [
                    'response' => $customerResponse ? $customerResponse->json() : null
                ]);
                return redirect()->back()->with('error', 'Müşteri oluşturulamadı.');
            }

            $customerId = $customerResponse->json()['data']['id'];

            // Checkout verilerini hazırla
            $checkoutData = [
                'amount' => $finalPrice,
                'currency' => 'EUR', // Sandbox'ta US/USD çalışmadı, DE/EUR kullanıyoruz
                'country' => 'DE',
                'complete_payment_url' => route('payment.success'),
                'error_payment_url' => route('payment.cancel'),
                'complete_checkout_url' => route('payment.success'),
                'cancel_checkout_url' => route('payment.cancel'),
                'merchant_reference_id' => $merchantReferenceId,
                'language' => 'en',
                'metadata' => [
                    'package_code' => $packageCode,
                    'customer_email' => $email,
                    'customer_name' => $name
                ],
                'payment_method_types_include' => ['de_visa_card', 'de_mastercard_card'],
                'customer' => $customerId,
                'cart_items' => [
                    [
                        'name' => $packageData['name'],
                        'amount' => $finalPrice,
                        'quantity' => 1,
                        'image' => asset('images/logo.png'),
                        'description' => "Data: " . (isset($packageData['volume']) ? round($packageData['volume'] / (1024 * 1024 * 1024), 2) : 'N/A') . " GB, Validity: {$packageData['duration']} days"
                    ]
                ]
            ];

            // Remove any null values to avoid issues with the API
            $this->removeNullValues($checkoutData);

            // Log the checkout request
            \Log::info('Rapyd checkout request:', [
                'data' => $checkoutData
            ]);

            $response = $this->rapydService->createCheckout($checkoutData);

            // Log the Rapyd API response
            \Log::info('Rapyd API response:', [
                'status' => $response->status(),
                'body' => $response->json(),
            ]);

            if ($response->successful()) {
                $checkoutData = $response->json()['data'];

                if (!isset($checkoutData['id']) || !isset($checkoutData['redirect_url'])) {
                    \Log::error('Checkout data missing required fields:', [
                        'response' => $checkoutData,
                        'required_fields' => ['id', 'redirect_url'],
                        'available_fields' => array_keys($checkoutData)
                    ]);
                    return redirect()->back()->with('error', 'Payment information is incomplete.');
                }

                // Payment kaydını hemen oluştur (status: pending)
                $payment = \App\Models\Payment::create([
                    'payment_id' => $checkoutData['id'],
                    'status' => 'pending', // Ödeme henüz tamamlanmadı
                    'merchant_reference_id' => $merchantReferenceId,
                    'order_id' => 'RAPYD_' . uniqid(),
                    'package_code' => $packageCode,
                    'email' => $email,
                    'name' => $name,
                    'payment_method' => 'rapyd',
                    'price_amount' => $finalPrice,
                    'price_currency' => 'EUR',
                    'rapyd_checkout_id' => $checkoutData['id']
                ]);

                \Log::info('Rapyd Payment record created:', [
                    'payment_id' => $payment->payment_id,
                    'merchant_reference_id' => $merchantReferenceId,
                    'status' => $payment->status,
                    'email' => $email
                ]);

                // Store checkout details in session for later use
                session(['rapyd_checkout_id' => $checkoutData['id']]);
                session(['package_code' => $packageCode]);
                session(['customer_email' => $email]);
                session(['customer_name' => $name]);
                session(['merchant_reference_id' => $merchantReferenceId]);

                // Redirect to Rapyd checkout page
                return redirect($checkoutData['redirect_url']);
            } else {
                \Log::error('Checkout creation failed:', [
                    'status' => $response->status(),
                    'response' => $response->json(),
                    'error_message' => $response->json()['message'] ?? 'Unknown error'
                ]);
                $errorMessage = $response->json()['message'] ?? 'Unknown error';
                return redirect()->back()->with('error', 'Payment could not be created: ' . $errorMessage);
            }
        } catch (\Exception $e) {
            \Log::error('Rapyd payment error:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ]);
            return redirect()->back()->with('error', 'An error occurred.');
        }
    }

    /**
     * Recursively remove null values from an array
     *
     * @param array &$data The array to process
     * @return void
     */
    private function removeNullValues(&$data)
    {
        foreach ($data as $key => &$value) {
            if (is_null($value)) {
                unset($data[$key]);
            } elseif (is_array($value)) {
                $this->removeNullValues($value);

                // If the array is now empty, remove it
                if (empty($value)) {
                    unset($data[$key]);
                }
            }
        }
    }

    private function processCryptoPayment($request)
    {
        try {
            $packageCode = $request->input('package_code');
            $email = $request->input('email');
            $name = $request->input('name');
            $payCurrency = strtolower($request->input('pay_currency'));

            $packageResponse = Http::withHeaders($this->headers)
                ->post("{$this->apiUrl}/api/v1/open/package/list", [
                    'packageCode' => $packageCode,
                ]);

            if (!$packageResponse->successful()) {
                \Log::error('Package fetch failed for payment:', [
                    'packageCode' => $packageCode,
                    'status' => $packageResponse->status(),
                    'response' => $packageResponse->json()  // JSON yanıtını loglayalım
                ]);
                return redirect()->back()->with('error', 'Paket bilgileri alınamadı.');
            }

            $packageData = collect($packageResponse->json()['obj']['packageList'])->firstWhere('packageCode', $packageCode);
            $price = $packageData['price'] * 1.3 / 10000;

            // NowPayments API isteği öncesi log
            \Log::info('NowPayments payment request:', [
                'price' => $price,
                'currency' => $payCurrency,
                'package_code' => $packageCode
            ]);

            $response = Http::withHeaders([
                'x-api-key' => config('services.nowpayments.api_key'),
                'Content-Type' => 'application/json',
            ])->post("{$this->nowPaymentsApiUrl}/payment", [
                'price_amount' => $price,
                'price_currency' => 'usd',
                'pay_currency' => $payCurrency,
                'order_id' => 'ESIM_' . uniqid(),
                'order_description' => "eSIM Purchase: {$packageData['name']}",
                'ipn_callback_url' => route('payment.callback'),
                'success_url' => route('payment.success'),
                'cancel_url' => route('payment.cancel'),
                'is_fee_paid_by_user' => false,
            ]);

            // NowPayments API yanıtını detaylı loglayalım
            \Log::info('NowPayments API response:', [
                'status' => $response->status(),
                'headers' => $response->headers(),
                'body' => $response->json(),
            ]);

            if ($response->successful()) {
                $paymentData = $response->json();

                if (!isset($paymentData['pay_address']) || !isset($paymentData['pay_amount'])) {
                    \Log::error('Payment data missing required fields:', [
                        'response' => $paymentData,
                        'required_fields' => ['pay_address', 'pay_amount'],
                        'available_fields' => array_keys($paymentData)
                    ]);
                    return redirect()->back()->with('error', 'Ödeme bilgileri eksik.');
                }

                // expiration_estimate_date değerini MySQL formatına çevir
                $expirationEstimateDate = Carbon::parse($paymentData['expiration_estimate_date'])->format('Y-m-d H:i:s');

                // Ödeme detaylarını MySQL tablosuna kaydet
                \App\Models\Payment::create([
                    'payment_id' => $paymentData['payment_id'],
                    'status' => $paymentData['payment_status'],
                    'pay_address' => $paymentData['pay_address'],
                    'pay_amount' => $paymentData['pay_amount'],
                    'pay_currency' => $paymentData['pay_currency'],
                    'price_amount' => $paymentData['price_amount'],
                    'price_currency' => $paymentData['price_currency'],
                    'order_id' => $paymentData['order_id'],
                    'package_code' => $packageCode,
                    'email' => $email,
                    'name' => $name,
                    'network' => $paymentData['network'],
                    'expiration_estimate_date' => $expirationEstimateDate,
                ]);

                $qrCode = QrCode::size(300)->generate($paymentData['pay_address']);
                $paymentDetails = [
                    'pay_address' => $paymentData['pay_address'],
                    'pay_amount' => $paymentData['pay_amount'],
                    'pay_currency' => $paymentData['pay_currency'],
                    'qr_code' => $qrCode,
                    'expiration_estimate_date' => $paymentData['expiration_estimate_date'],
                    'network' => $paymentData['network'],
                ];

                return view('esim.payment', $paymentDetails);
            } else {
                \Log::error('Payment creation failed:', [
                    'status' => $response->status(),
                    'headers' => $response->headers(),
                    'response' => $response->json(),
                    'error_message' => $response->json()['message'] ?? 'Bilinmeyen hata'
                ]);
                $errorMessage = $response->json()['message'] ?? 'Bilinmeyen hata';
                return redirect()->back()->with('error', 'Ödeme oluşturulamadı: ' . $errorMessage);
            }
        } catch (\Exception $e) {
            \Log::error('Crypto payment error:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ]);
            return redirect()->back()->with('error', 'Bir hata oluştu.');
        }
    }


    public function paymentCallback(Request $request)
    {
        \Log::info('Payment callback received:', [
            'query' => $request->query(),
            'input' => $request->input(),
            'all' => $request->all(),
            'url' => $request->fullUrl()
        ]);

        // merchant_reference_id veya checkout_id’yi kontrol et
        $merchantReferenceId = $request->query('merchant_reference_id') ?? $request->input('merchant_reference_id');
        $checkoutId = $request->query('checkout_id') ?? $request->input('checkout_id');

        // Ödeme kaydını bul
        $payment = null;
        if ($merchantReferenceId || $checkoutId) {
            $query = \App\Models\Payment::query();
            if ($merchantReferenceId) {
                $query->where('merchant_reference_id', $merchantReferenceId)
                      ->orWhere('order_id', $merchantReferenceId);
            }
            if ($checkoutId) {
                $query->orWhere('rapyd_checkout_id', $checkoutId);
            }
            $payment = $query->first();
        }

        // Ödeme kaydı bulunamazsa, son oluşturulan kaydı kontrol et
        if (!$payment) {
            $payment = \App\Models\Payment::where('payment_method', 'rapyd')
                ->where('status', 'pending')
                ->latest()
                ->first();

            if ($payment) {
                \Log::info('Fallback: Using latest pending payment:', [
                    'payment_id' => $payment->payment_id,
                    'merchant_reference_id' => $payment->merchant_reference_id
                ]);
                $merchantReferenceId = $payment->merchant_reference_id;
            }
        }

        if ($payment) {
            \Log::info('Payment found:', [
                'payment_id' => $payment->payment_id,
                'merchant_reference_id' => $payment->merchant_reference_id
            ]);
            $payment->update(['status' => 'pending']);
        } else {
            \Log::warning('Payment not found in callback:', [
                'merchant_reference_id' => $merchantReferenceId,
                'checkout_id' => $checkoutId,
                'request' => $request->all()
            ]);
            // Hata durumunda anasayfaya değil, hata sayfasına yönlendir
            return redirect()->route('payment.cancel')->with('error', 'Ödeme doğrulanamadı.');
        }

        // payment.success’e yönlendir
        $redirectParams = array_filter([
            'merchant_reference_id' => $merchantReferenceId,
            'checkout_id' => $checkoutId
        ]);
        return redirect()->route('payment.success', $redirectParams);
    }

    private function createEsimOrder($packageCode, $email, $name)
    {
        try {
            // İlk sipariş oluşturma isteği
            $orderResponse = Http::withHeaders($this->headers)
                ->post("{$this->apiUrl}/api/v1/open/esim/order", [
                    'packageCode' => $packageCode,
                    'customerEmail' => $email,
                    'customerName' => $name,
                    'count' => 1,
                    'transactionId' => uniqid('ESIM_'),
                    'packageInfoList' => [
                        [
                            'packageCode' => $packageCode,
                            'count' => 1
                        ]
                    ]
                ]);

            if (!$orderResponse->successful()) {
                throw new \Exception('eSIM siparişi oluşturulamadı');
            }

            $orderData = $orderResponse->json()['obj'];

            // Maksimum 5 deneme ve her deneme arasında 3 saniye bekle
            $maxAttempts = 5;
            $attempt = 0;
            $esimDetails = null;

            while ($attempt < $maxAttempts) {
                sleep(3); // 3 saniye bekle

                $detailsResponse = Http::withHeaders($this->headers)
                    ->post("{$this->apiUrl}/api/v1/open/esim/query", [
                        'orderNo' => $orderData['orderNo'],
                        'pager' => [
                            'page' => 1,
                            'pageSize' => 10
                        ]
                    ]);

                if ($detailsResponse->successful()) {
                    $esimDetails = $detailsResponse->json()['obj']['esimList'][0] ?? null;

                    // eSIM detayları hazır mı kontrol et
                    if ($esimDetails &&
                        !empty($esimDetails['iccid']) &&
                        !empty($esimDetails['ac']) &&
                        !empty($esimDetails['pin'])) {
                        break; // Tüm gerekli bilgiler hazırsa döngüden çık
                    }
                }

                $attempt++;
                \Log::info("eSIM details attempt $attempt of $maxAttempts");
            }

            if (!$esimDetails) {
                // Email'i geçici bilgilerle gönder ve asenkron işlem başlat
                dispatch(new \App\Jobs\SendDelayedEsimDetails($orderData['orderNo'], $email));

                return [
                    'orderNo' => $orderData['orderNo'],
                    'status' => 'pending',
                    'message' => 'eSIM detayları hazırlanıyor. Bilgiler email adresinize gönderilecektir.'
                ];
            }

            // QR kod görüntüsünü indir
            $qrCodeImage = null;
            if (!empty($esimDetails['qrCodeUrl'])) {
                $qrResponse = Http::get($esimDetails['qrCodeUrl']);
                if ($qrResponse->successful()) {
                    $qrCodeImage = $qrResponse->body();
                }
            }

            $packageInfo = $esimDetails['packageList'][0] ?? [];

            return [
                'orderNo' => $esimDetails['orderNo'],
                'iccid' => $esimDetails['iccid'],
                'activationCode' => $esimDetails['ac'],
                'qrCodeImage' => $qrCodeImage,
                'qrCodeUrl' => $esimDetails['qrCodeUrl'],
                'pin' => $esimDetails['pin'],
                'puk' => $esimDetails['puk'],
                'apn' => $esimDetails['apn'],
                'expiredTime' => $esimDetails['expiredTime'],
                'packageName' => $packageInfo['packageName'] ?? '',
                'data' => isset($packageInfo['volume']) ? round($packageInfo['volume'] / (1024 * 1024 * 1024), 2) . ' GB' : '',
                'validity' => $packageInfo['duration'] ?? ''
            ];

        } catch (\Exception $e) {
            \Log::error('eSIM order error:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function paymentSuccess(Request $request)
    {
        try {
            \Log::info('Payment success callback received:', [
                'query' => $request->query(),
                'input' => $request->input(),
                'all' => $request->all(),
                'session' => session()->all()
            ]);
    
            // Rapyd ödeme kontrolü
            $metadata = null;
            $paymentMethod = 'rapyd';
            $isApiRequest = $request->expectsJson() || $request->is('api/*');
    
            // Önce Stripe kontrolü
            if ($request->query('payment_intent')) {
                $paymentIntentId = $request->query('payment_intent');
                if (!$paymentIntentId) {
                    \Log::error('Stripe payment intent missing', ['request' => $request->all()]);
                    if ($isApiRequest) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Stripe ödeme kimliği sağlanmadı.'
                        ], 400);
                    }
                    return redirect()->route('payment.cancel')->with('error', 'Stripe ödeme kimliği sağlanmadı.');
                }
    
                \Stripe\Stripe::setApiKey(config('services.stripe.secret'));
                $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId);
    
                $payment = \App\Models\Payment::where('payment_id', $paymentIntentId)
                    ->where('payment_method', 'card')
                    ->first();
    
                if (!$payment) {
                    \Log::error('Stripe payment record not found:', [
                        'payment_intent_id' => $paymentIntentId
                    ]);
                    if ($isApiRequest) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Stripe ödeme kaydı bulunamadı.'
                        ], 404);
                    }
                    return redirect()->route('payment.cancel')->with('error', 'Stripe ödeme kaydı bulunamadı.');
                }
    
                if ($payment->status === 'sended') {
                    \Log::info('Stripe payment already processed:', [
                        'payment_intent_id' => $paymentIntentId
                    ]);
                    if ($isApiRequest) {
                        return response()->json([
                            'success' => true,
                            'message' => 'Ödeme zaten işlendi ve eSIM gönderildi.',
                            'data' => [
                                'order_no' => $payment->order_no
                            ]
                        ], 200);
                    }
                    return view('esim.success', [
                        'paymentMethod' => 'card',
                        'orderNo' => $payment->order_no,
                        'message' => 'eSIM zaten gönderildi.'
                    ]);
                }
    
                $metadata = $paymentIntent->metadata;
                $paymentMethod = 'card';
    
                // Ödeme durumunu success yap
                $payment->update(['status' => 'success']);
            // GET parametrelerinden checkout_id veya merchant_reference_id kontrolü (Rapyd için)
            } elseif ($request->query('checkout_id') || $request->query('merchant_reference_id') || session('rapyd_checkout_id') || session('merchant_reference_id')) {
                $checkoutId = $request->query('checkout_id') ?? session('rapyd_checkout_id');
                $merchantReferenceId = $request->query('merchant_reference_id') ?? session('merchant_reference_id');
    
                // Ödeme kaydını bul
                $payment = null;
                if ($checkoutId || $merchantReferenceId) {
                    $payment = \App\Models\Payment::where('rapyd_checkout_id', $checkoutId)
                        ->orWhere('merchant_reference_id', $merchantReferenceId)
                        ->where('payment_method', 'rapyd')
                        ->first();
                }
    
                if (!$payment) {
                    \Log::error('Rapyd payment record not found:', [
                        'checkout_id' => $checkoutId,
                        'merchant_reference_id' => $merchantReferenceId
                    ]);
                    if ($isApiRequest) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Rapyd ödeme kaydı bulunamadı.'
                        ], 404);
                    }
                    return redirect()->route('payment.cancel')->with('error', 'Rapyd ödeme kaydı bulunamadı.');
                }
    
                // Durum kontrolü
                if ($payment->status === 'sended') {
                    \Log::info('Rapyd payment already processed:', [
                        'payment_id' => $payment->payment_id,
                        'merchant_reference_id' => $merchantReferenceId
                    ]);
                    session()->forget(['rapyd_checkout_id', 'package_code', 'customer_email', 'customer_name', 'merchant_reference_id']);
                    if ($isApiRequest) {
                        return response()->json([
                            'success' => true,
                            'message' => 'Ödeme zaten işlendi ve eSIM gönderildi.',
                            'data' => [
                                'order_no' => $payment->order_no
                            ]
                        ], 200);
                    }
                    return view('esim.success', [
                        'paymentMethod' => $paymentMethod,
                        'orderNo' => $payment->order_no,
                        'message' => 'eSIM zaten gönderildi.'
                    ]);
                }
    
                // Metadata’yı ödeme kaydından al
                $metadata = (object)[
                    'package_code' => $payment->package_code,
                    'customer_email' => $payment->email,
                    'customer_name' => $payment->name
                ];
    
                // Ödeme durumunu success yap
                $payment->update(['status' => 'success']);
                session()->forget(['rapyd_checkout_id', 'package_code', 'customer_email', 'customer_name', 'merchant_reference_id']);
            // Crypto ödeme kontrolü (NowPayments)
            } elseif ($request->query('payment_id')) {
                $paymentId = $request->query('payment_id');
                $payment = \App\Models\Payment::where('payment_id', $paymentId)
                    ->where('payment_method', 'crypto')
                    ->first();
    
                if (!$payment) {
                    \Log::error('Crypto payment not found:', [
                        'payment_id' => $paymentId
                    ]);
                    if ($isApiRequest) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Kripto ödeme kaydı bulunamadı.'
                        ], 404);
                    }
                    return redirect()->route('payment.cancel')->with('error', 'Kripto ödeme kaydı bulunamadı.');
                }
    
                if ($payment->status === 'sended') {
                    \Log::info('Crypto payment already processed:', [
                        'payment_id' => $paymentId
                    ]);
                    if ($isApiRequest) {
                        return response()->json([
                            'success' => true,
                            'message' => 'Ödeme zaten işlendi ve eSIM gönderildi.',
                            'data' => [
                                'order_no' => $payment->order_no
                            ]
                        ], 200);
                    }
                    return view('esim.success', [
                        'paymentMethod' => 'crypto',
                        'orderNo' => $payment->order_no,
                        'message' => 'eSIM zaten gönderildi.'
                    ]);
                }
    
                $metadata = (object)[
                    'package_code' => $payment->package_code,
                    'customer_email' => $payment->email,
                    'customer_name' => $payment->name
                ];
                $paymentMethod = 'crypto';
    
                $payment->update(['status' => 'success']);
            } else {
                \Log::error('No payment identifiers provided', [
                    'request' => $request->all(),
                    'session' => session()->all()
                ]);
                if ($isApiRequest) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Ödeme kimliği sağlanmadı. Rapyd, Stripe veya Crypto ödeme kimliği gerekli.'
                    ], 400);
                }
                return redirect()->route('payment.cancel')->with('error', 'Ödeme kimliği sağlanmadı. Lütfen ödeme işlemini tekrar deneyin.');
            }
    
            // E-posta doğrulama
            if (!filter_var($metadata->customer_email, FILTER_VALIDATE_EMAIL)) {
                \Log::error('Invalid email address:', ['email' => $metadata->customer_email]);
                if ($isApiRequest) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Geçersiz e-posta adresi.'
                    ], 400);
                }
                return redirect()->route('payment.cancel')->with('error', 'Geçersiz e-posta adresi.');
            }
    
            // eSIM siparişi oluştur (sadece success durumunda)
            if ($payment->status === 'success') {
                $esimOrder = $this->createEsimOrder(
                    $metadata->package_code,
                    $metadata->customer_email,
                    $metadata->customer_name
                );
    
                // API yanıtını logla
                \Log::info('eSIM order response:', [
                    'order' => \Illuminate\Support\Arr::except($esimOrder, ['qrCodeImage']),
                    'has_qr' => !empty($esimOrder['qrCodeImage']) || !empty($esimOrder['qrCodeUrl']),
                    'has_iccid' => isset($esimOrder['iccid']),
                    'has_activation' => isset($esimOrder['activationCode']),
                    'has_manual' => isset($esimOrder['manualCode'])
                ]);
    
                // Eğer eSIM detayları hazır değilse
                if (isset($esimOrder['status']) && $esimOrder['status'] === 'pending') {
                    $payment->update([
                        'status' => 'sended',
                        'order_no' => $esimOrder['orderNo'],
                        'esim_order_no' => $esimOrder['orderNo'],
                        'esim_iccid' => $esimOrder['iccid'] ?? null,
                        'esim_activation_code' => $esimOrder['activationCode'] ?? null,
                        'esim_pin' => $esimOrder['pin'] ?? null,
                        'esim_puk' => $esimOrder['puk'] ?? null,
                        'esim_qr_url' => $esimOrder['qrCodeUrl'] ?? null,
                        'esim_tran_no' => $esimOrder['esimTranNo'] ?? null  // Yeni eklenen alan
                    ]);
                    if ($isApiRequest) {
                        return response()->json([
                            'success' => true,
                            'message' => $esimOrder['message'],
                            'data' => [
                                'order_no' => $esimOrder['orderNo'],
                                'status' => 'pending'
                            ]
                        ], 200);
                    }
                    return view('esim.success', [
                        'paymentMethod' => $paymentMethod,
                        'orderNo' => $esimOrder['orderNo'],
                        'pendingMessage' => $esimOrder['message']
                    ]);
                }
    
                // Paket detaylarını al
                $packageResponse = Http::withHeaders($this->headers)
                    ->post("{$this->apiUrl}/api/v1/open/package/list", [
                        'packageCode' => $metadata->package_code,
                    ]);
    
                $packageData = collect($packageResponse->json()['obj']['packageList'])
                    ->firstWhere('packageCode', $metadata->package_code);
    
                if (!$packageData) {
                    \Log::error('Package not found:', ['package_code' => $metadata->package_code]);
                    if ($isApiRequest) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Paket bilgileri alınamadı.'
                        ], 400);
                    }
                    return redirect()->route('payment.cancel')->with('error', 'Paket bilgileri alınamadı.');
                }
    
                // QR kodu kontrolü ve işleme
                $qrCodeImage = null;
                if (!empty($esimOrder['qrCodeImage'])) {
                    $qrCodeImage = $esimOrder['qrCodeImage'];
                } elseif (!empty($esimOrder['qrCodeUrl'])) {
                    $qrResponse = Http::get($esimOrder['qrCodeUrl']);
                    if ($qrResponse->successful()) {
                        $qrCodeImage = $qrResponse->body();
                    }
                } else {
                    \Log::warning('QR code not available in eSIM order response');
                }
    
                // Mail için sipariş detaylarını hazırla
                $orderDetails = [
                    'customerName' => $metadata->customer_name,
                    'packageName' => $packageData['name'],
                    'data' => isset($packageData['volume']) ? round($packageData['volume'] / (1024 * 1024 * 1024), 2) . ' GB' : 'Belirtilmemiş',
                    'validity' => $packageData['duration'] ?? 'Belirtilmemiş',
                    'qrCodeImage' => $qrCodeImage,
                    'qrCodeUrl' => $esimOrder['qrCodeUrl'] ?? '',
                    'iccid' => $esimOrder['iccid'] ?? '',
                    'activationCode' => $esimOrder['activationCode'] ?? '',
                    'pin' => $esimOrder['pin'] ?? '',
                    'puk' => $esimOrder['puk'] ?? '',
                    'apn' => $esimOrder['apn'] ?? '',
                    'expiredTime' => $esimOrder['expiredTime'] ?? '',
                    'manualCode' => $esimOrder['manualCode'] ?? ''
                ];
    
                // Mail gönder
                Mail::to($metadata->customer_email)
                    ->send(new EsimPurchaseConfirmation($orderDetails));
    
                // Durumu sended’e güncelle ve eSIM detaylarını kaydet
                $orderNo = $esimOrder['orderNo'];

                // Query endpoint'ine istek at
                $detailsResponse = Http::withHeaders($this->headers)
                    ->post("{$this->apiUrl}/api/v1/open/esim/query", [
                        'orderNo' => $orderNo,
                        'pager' => [
                            'page' => 1,
                            'pageSize' => 10
                        ]
                    ]);

                \Log::info('eSIM query response:', [
                    'response' => $detailsResponse->json()
                ]);

                if ($detailsResponse->successful()) {
                    $esimDetails = $detailsResponse->json()['obj']['esimList'][0] ?? null;
                    
                    $payment->update([
                        'status' => 'sended',
                        'order_no' => $orderNo,
                        'esim_order_no' => $orderNo,  // Eklendi
                        'esim_iccid' => $esimOrder['iccid'] ?? null,
                        'esim_activation_code' => $esimOrder['activationCode'] ?? null,
                        'esim_pin' => $esimOrder['pin'] ?? null,
                        'esim_puk' => $esimOrder['puk'] ?? null,
                        'esim_qr_url' => $esimOrder['qrCodeUrl'] ?? null,
                        'esim_tran_no' => $esimDetails['esimTranNo'] ?? null
                    ]);
                }
    
                if ($isApiRequest) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Ödeme doğrulandı, eSIM gönderildi.',
                        'data' => [
                            'payment_method' => $paymentMethod,
                            'order_no' => $esimOrder['orderNo'],
                            'esim_details' => [
                                'iccid' => $esimOrder['iccid'] ?? null,
                                'activationCode' => $esimOrder['activationCode'] ?? null,
                                'qrCodeUrl' => $esimOrder['qrCodeUrl'] ?? null,
                                'manualCode' => $esimOrder['manualCode'] ?? null,
                            ]
                        ]
                    ], 200);
                }
    
                return view('esim.success', [
                    'paymentMethod' => $paymentMethod,
                    'orderNo' => $esimOrder['orderNo']
                ]);
            }
    
            \Log::error('Unexpected payment status:', [
                'payment_id' => $payment->payment_id,
                'status' => $payment->status
            ]);
            if ($isApiRequest) {
                return response()->json([
                    'success' => false,
                    'message' => 'Geçersiz ödeme durumu.'
                ], 400);
            }
            return redirect()->route('payment.cancel')->with('error', 'Geçersiz ödeme durumu.');
    
        } catch (\Exception $e) {
            \Log::error('Payment success handling error:', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
                'session' => session()->all()
            ]);
            if ($isApiRequest) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bir hata oluştu: ' . $e->getMessage()
                ], 500);
            }
            return redirect()->route('payment.cancel')->with('error', 'Bir hata oluştu, lütfen müşteri hizmetleri ile iletişime geçin.');
        }
    }


    public function paymentCancel()
    {
        return redirect()->route('home')->with('error', 'Ödeme iptal edildi.');
    }

    public function checkUsage($esimTranNo)
    {
        $esimService = new EsimService();
        $usageDetails = $esimService->getUsageDetails($esimTranNo);

        if (!$usageDetails) {
            return response()->json([
                'success' => false,
                'message' => 'Kullanım bilgileri alınamadı'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $usageDetails
        ]);
    }

    public function purchaseTopup(Request $request)
    {
        $request->validate([
            'esim_tran_no' => 'required|string',
            'package_code' => 'required|string',
            'email' => 'required|email',
            'name' => 'required|string'
        ]);

        try {
            // Paket bilgilerini al
            $response = Http::withHeaders($this->headers)
                ->post("{$this->apiUrl}/api/v1/open/package/list");

            if (!$response->successful()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Paket bilgileri alınamadı'
                ], 400);
            }

            $packages = $response->json()['obj']['packageList'] ?? [];
            $packageData = collect($packages)->firstWhere('code', $request->package_code);

            if (!$packageData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Paket bulunamadı'
                ], 404);
            }

            $price = $packageData['retail_price'];
            $merchantReferenceId = 'TOPUP_' . uniqid();

            // Rapyd checkout oluştur
            $checkoutData = [
                'amount' => $price,
                'currency' => 'EUR',
                'country' => 'DE',
                'complete_payment_url' => route('payment.success'),
                'error_payment_url' => route('payment.cancel'),
                'complete_checkout_url' => route('payment.success'),
                'cancel_checkout_url' => route('payment.cancel'),
                'merchant_reference_id' => $merchantReferenceId,
                'language' => 'en',
                'metadata' => [
                    'type' => 'topup',
                    'package_code' => $request->package_code,
                    'esim_tran_no' => $request->esim_tran_no,
                    'customer_email' => $request->email,
                    'customer_name' => $request->name
                ],
                'payment_method_types_include' => ['de_visa_card', 'de_mastercard_card'],
                'cart_items' => [
                    [
                        'name' => $packageData['name'],
                        'amount' => $price,
                        'quantity' => 1,
                        'description' => "Data: " . (isset($packageData['volume']) ? round($packageData['volume'] / (1024 * 1024 * 1024), 2) : 'N/A') . " GB, Validity: {$packageData['duration']} days"
                    ]
                ]
            ];

            // Rapyd checkout oluştur
            $checkout = $this->createRapydCheckout($checkoutData);

            return response()->json([
                'success' => true,
                'checkout_id' => $checkout['id'],
                'checkout_url' => $checkout['redirect_url']
            ]);

        } catch (\Exception $e) {
            \Log::error('Topup purchase error:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'İşlem sırasında bir hata oluştu'
            ], 500);
        }
    }

    public function resendEsimEmail(Request $request)
    {
        $request->validate([
            'orderNo' => 'required|string'
        ]);
        
        $orderNo = $request->orderNo;
        $user = Auth::user();
        
        // Sipariş bilgilerini kontrol et
        $order = DB::table('payments')
            ->where('order_no', $orderNo)
            ->where('email', $user->email)
            ->first();
        
        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found or you do not have permission to access it.'
            ]);
        }
        
        // eSIM detaylarını API'den al
        $response = Http::withHeaders($this->headers)
            ->post("{$this->apiUrl}/api/v1/open/esim/query", [
                'orderNo' => $orderNo,
                'pager' => [
                    'page' => 1,
                    'pageSize' => 10
                ]
            ]);
        
        $responseData = $response->json();
        
        // API yanıtını logla
        \Log::info('eSIM API Response:', [
            'response' => $responseData
        ]);
        
        if (!isset($responseData['obj']) || !isset($responseData['obj']['esimList']) || empty($responseData['obj']['esimList'])) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve eSIM details from provider.'
            ]);
        }
        
        $esimDetails = $responseData['obj']['esimList'][0];
        
        // QR kodu görüntüsünü al
        $qrCodeImage = null;
        if (isset($esimDetails['qrCodeUrl']) && !empty($esimDetails['qrCodeUrl'])) {
            try {
                $qrResponse = Http::get($esimDetails['qrCodeUrl']);
                if ($qrResponse->successful()) {
                    $qrCodeImage = $qrResponse->body();
                }
            } catch (\Exception $e) {
                \Log::error('Failed to get QR code image: ' . $e->getMessage());
            }
        }
        
        // E-posta için sipariş detaylarını hazırla
        $packageInfo = $esimDetails['packageList'][0] ?? [];
        
        $orderDetails = [
            'orderNo' => $esimDetails['orderNo'],
            'iccid' => $esimDetails['iccid'],
            'activationCode' => $esimDetails['ac'],
            'qrCodeImage' => $qrCodeImage,
            'qrCodeUrl' => $esimDetails['qrCodeUrl'],
            'pin' => $esimDetails['pin'],
            'puk' => $esimDetails['puk'],
            'apn' => $esimDetails['apn'],
            'expiredTime' => $esimDetails['expiredTime'],
            'packageName' => $packageInfo['packageName'] ?? '',
            'data' => isset($packageInfo['volume']) ? round($packageInfo['volume'] / (1024 * 1024 * 1024), 2) . ' GB' : '',
            'validity' => $packageInfo['duration'] ?? '',
            'customerName' => $user->name
        ];
        
        // E-postayı gönder
        try {
            Mail::to($user->email)->send(new EsimPurchaseConfirmation($orderDetails));
            
            return response()->json([
                'success' => true,
                'message' => 'eSIM details have been sent to your email address.'
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to send eSIM email: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to send email. Please try again later.'
            ]);
        }
    }

    public function getPackageStatus($orderNo)
    {
        $user = Auth::user();
        
        // Sipariş bilgilerini kontrol et
        $order = DB::table('payments')
            ->where('order_no', $orderNo)
            ->where('email', $user->email)
            ->first();
        
        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Sipariş bulunamadı veya bu siparişe erişim izniniz yok.'
            ]);
        }
        
        // eSIM detaylarını API'den al
        $response = Http::withHeaders($this->headers)
            ->post("{$this->apiUrl}/api/v1/open/esim/query", [
                'orderNo' => $orderNo,
                'pager' => [
                    'page' => 1,
                    'pageSize' => 10
                ]
            ]);
        
        if (!$response->successful()) {
            Log::error('eSIM Query Error', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'API isteği başarısız oldu: ' . $response->status()
            ]);
        }
        
        $data = $response->json();
        Log::info('eSIM Query Response', ['data' => $data]);
        
        if (!isset($data['obj']['esimList']) || empty($data['obj']['esimList'])) {
            return response()->json([
                'success' => false,
                'message' => 'Bu sipariş numarasıyla ilişkili eSIM bulunamadı'
            ]);
        }
        
        $esimDetails = $data['obj']['esimList'][0];
        
        // esimTranNo'yu alıp kullanım bilgilerini sorgula
        if (!isset($esimDetails['esimTranNo'])) {
            return response()->json([
                'success' => false,
                'message' => 'eSIM transaction numarası bulunamadı'
            ]);
        }
        
        $esimTranNo = $esimDetails['esimTranNo'];
        $esimService = new EsimService();
        $usageDetails = $esimService->getUsageDetails($esimTranNo);
        
        if (!$usageDetails) {
            return response()->json([
                'success' => false,
                'message' => 'Kullanım bilgileri alınamadı'
            ]);
        }
        
        // Son kullanma tarihini hesapla
        $expiryDate = isset($esimDetails['expiredTime']) ? Carbon::parse($esimDetails['expiredTime']) : null;
        $remainingDays = $expiryDate ? now()->diffInDays($expiryDate, false) : 0;
        $remainingDays = max(0, $remainingDays);
        
        return response()->json([
            'success' => true,
            'remainingData' => $usageDetails['dataUsage'],
            'totalData' => $usageDetails['totalData'],
            'usagePercentage' => $usageDetails['usagePercentage'],
            'lastUpdateTime' => $usageDetails['lastUpdateTime'],
            'remainingDays' => $remainingDays,
            'expiryDate' => $expiryDate ? $expiryDate->format('d.m.Y H:i') : 'N/A'
        ]);
    }
}






























