namespace App\Services;

class EsimApiService
{
    private $headers;
    private $apiUrl;

    public function __construct()
    {
        $this->apiUrl = config('services.esim.api_url');
        $this->headers = [
            'RT-AccessCode' => config('services.esim.access_code'),
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    public function getLocations()
    {
        // Mevcut getLocations metodunun içeriği
    }

    public function getPackages($locationCode)
    {
        // Mevcut paket alma mantığı
    }
}