@extends('layouts.app')

@section('content')
<!-- Payment Header -->
<section class="py-4 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="javascript:history.back()">Checkout</a></li>
                <li class="breadcrumb-item active">Payment</li>
            </ol>
        </nav>
        <h1 class="mt-2">Credit Card Payment</h1>
    </div>
</section>

<!-- Checkout Steps -->
<section class="py-4">
    <div class="container">
        <div class="checkout-steps">
            <div class="step active">
                <div class="step-number">1</div>
                <div class="step-label">Package Selection</div>
            </div>
            <div class="step active">
                <div class="step-number">2</div>
                <div class="step-label">Checkout</div>
            </div>
            <div class="step active">
                <div class="step-number">3</div>
                <div class="step-label">Payment</div>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-label">Confirmation</div>
            </div>
        </div>
    </div>
</section>

<!-- Payment Form -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <!-- Order Summary -->
            <div class="col-lg-4 order-lg-2 mb-4">
                <div class="checkout-container">
                    <h4 class="mb-3">Order Summary</h4>
                    <div class="package-details">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">{{ $packageData['name'] }}</span>
                            <span>${{ number_format($amount, 2) }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Data</span>
                            <span>{{ isset($packageData['volume']) ? round($packageData['volume'] / (1024 * 1024 * 1024), 2) . ' GB' : 'Not specified' }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span>Validity</span>
                            <span>{{ $packageData['duration'] ?? 'Not specified' }} days</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>Total</span>
                            <span>${{ number_format($amount, 2) }}</span>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Your eSIM will be delivered to your email immediately after payment.
                        </div>
                    </div>
                    <div class="customer-info mt-4">
                        <h5 class="mb-3">Customer Information</h5>
                        <p class="mb-1"><strong>Name:</strong> {{ $customerName ?? 'Not provided' }}</p>
                        <p><strong>Email:</strong> {{ $customerEmail ?? 'Not provided' }}</p>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="col-lg-8 order-lg-1">
                <div class="checkout-container">
                    <h4 class="mb-4">Enter Payment Details</h4>
                    <div class="card-icons mb-4 d-flex justify-content-center">
                        <i class="fab fa-cc-visa fa-2x mx-2"></i>
                        <i class="fab fa-cc-mastercard fa-2x mx-2"></i>
                        <i class="fab fa-cc-amex fa-2x mx-2"></i>
                        <i class="fab fa-cc-discover fa-2x mx-2"></i>
                    </div>

                    <form id="payment-form">
                        <div id="payment-element" class="mb-4"></div>
                        <button id="submit" class="btn btn-primary btn-lg w-100">
                            <div class="spinner hidden" id="spinner"></div>
                            <span id="button-text">Complete Payment</span>
                        </button>
                        <div id="payment-message" class="hidden alert alert-danger mt-3"></div>
                    </form>

                    <div class="secure-payment-info mt-4 text-center">
                        <i class="fas fa-lock me-2"></i>
                        <small>Your payment information is secure. We use industry-standard encryption to protect your data.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script>
const stripe = Stripe('{{ $stripeKey }}');
const clientSecret = '{{ $clientSecret }}';

const appearance = {
    theme: 'stripe',
    variables: {
        colorPrimary: '#3a36e0',
        colorBackground: '#ffffff',
        colorText: '#333333',
        colorDanger: '#dc3545',
        fontFamily: 'Poppins, sans-serif',
        borderRadius: '5px',
    }
};
const elements = stripe.elements({ appearance, clientSecret });

const paymentElement = elements.create("payment");
paymentElement.mount("#payment-element");

const form = document.getElementById('payment-form');
const submitButton = document.getElementById('submit');
const spinner = document.getElementById('spinner');
const messageDiv = document.getElementById('payment-message');

form.addEventListener('submit', async (e) => {
    e.preventDefault();
    submitButton.disabled = true;
    spinner.classList.remove('hidden');
    document.getElementById('button-text').textContent = 'Processing...';

    const {error} = await stripe.confirmPayment({
        elements,
        confirmParams: {
            return_url: "{{ route('payment.success') }}?payment_intent={PAYMENT_INTENT}",
        },
    });

    if (error) {
        messageDiv.textContent = error.message;
        messageDiv.classList.remove('hidden');
        submitButton.disabled = false;
        spinner.classList.add('hidden');
        document.getElementById('button-text').textContent = 'Complete Payment';

        // Scroll to error message
        messageDiv.scrollIntoView({ behavior: 'smooth' });
    }
});
</script>

<style>
.hidden {
    display: none;
}
.spinner {
    width: 20px;
    height: 20px;
    border: 3px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 10px;
}
@keyframes spin {
    to {transform: rotate(360deg);}
}

.card-icons i {
    color: #6c757d;
    transition: color 0.3s ease;
}
.card-icons i:hover {
    color: #3a36e0;
}
</style>
@endpush
@endsection