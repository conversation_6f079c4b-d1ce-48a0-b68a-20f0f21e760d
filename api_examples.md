# Vox eSIM API Kullanım Örnekleri

## Ödeme Linki Oluşturma

### İstek

```http
POST /api/payment/create-link HTTP/1.1
Host: yourdomain.com
Content-Type: application/json
Accept: application/json

{
  "package_code": "GL-144_6_15",
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "+905551234567",
  "callback_url": "https://yourapp.com/payment/callback"
}
```

### Başar<PERSON><PERSON><PERSON>t

```json
{
  "success": true,
  "message": "Payment link created successfully.",
  "data": {
    "payment_id": "checkout_*********",
    "merchant_reference_id": "ESIM_60a7b3c4d5e6f",
    "payment_url": "https://checkout.rapyd.net/checkout/*********",
    "package": {
      "code": "GL-144_6_15",
      "name": "Global Traveler",
      "data": "5 GB",
      "validity": "15 days",
      "price": 24.99,
      "currency": "EUR"
    }
  }
}
```

### <PERSON>a <PERSON>ı

```json
{
  "success": false,
  "message": "Package information could not be retrieved.",
  "error": {
    "status": "error",
    "message": "Invalid package code"
  }
}
```

## Ödeme Durumu Sorgulama

### İstek

```http
POST /api/payment/verify HTTP/1.1
Host: yourdomain.com
Content-Type: application/json
Accept: application/json

{
  "merchant_reference_id": "ESIM_60a7b3c4d5e6f"
}
```

### Başarılı Yanıt

```json
{
  "success": true,
  "data": {
    "payment_id": "checkout_*********",
    "merchant_reference_id": "ESIM_60a7b3c4d5e6f",
    "status": "completed",
    "package_code": "GL-144_6_15",
    "email": "<EMAIL>",
    "name": "John Doe",
    "payment_method": "rapyd",
    "price_amount": 24.99,
    "price_currency": "EUR",
    "created_at": "2025-04-12T10:15:30.000000Z",
    "updated_at": "2025-04-12T10:20:45.000000Z"
  }
}
```

### Hata Yanıtı

```json
{
  "success": false,
  "message": "Payment not found."
}
```

## Mobil Uygulama Entegrasyonu

Mobil uygulamanızda aşağıdaki adımları izleyerek ödeme işlemini gerçekleştirebilirsiniz:

1. Kullanıcıdan gerekli bilgileri (ad, e-posta, telefon) alın
2. `/api/payment/create-link` endpoint'ine istek gönderin
3. Dönen yanıttaki `payment_url` değerini kullanarak kullanıcıyı ödeme sayfasına yönlendirin (WebView veya harici tarayıcı kullanabilirsiniz)
4. Kullanıcı ödemeyi tamamladıktan sonra, `callback_url` parametresinde belirttiğiniz URL'e yönlendirilecektir
5. Ödeme durumunu kontrol etmek için `/api/payment/verify` endpoint'ini kullanabilirsiniz

### Örnek Mobil Uygulama Kodu (Flutter)

```dart
Future<void> createPayment() async {
  final response = await http.post(
    Uri.parse('https://yourdomain.com/api/payment/create-link'),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    body: jsonEncode({
      'package_code': 'GL-144_6_15',
      'name': 'John Doe',
      'email': '<EMAIL>',
      'phone': '+905551234567',
      'callback_url': 'https://yourapp.com/payment/callback'
    }),
  );

  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    final paymentUrl = data['data']['payment_url'];
    final merchantReferenceId = data['data']['merchant_reference_id'];
    
    // Save merchant_reference_id for later verification
    await savePaymentReference(merchantReferenceId);
    
    // Open payment URL in WebView or external browser
    await launchUrl(Uri.parse(paymentUrl));
  } else {
    // Handle error
    print('Error: ${response.body}');
  }
}

Future<void> verifyPayment(String merchantReferenceId) async {
  final response = await http.post(
    Uri.parse('https://yourdomain.com/api/payment/verify'),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    body: jsonEncode({
      'merchant_reference_id': merchantReferenceId
    }),
  );

  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    final paymentStatus = data['data']['status'];
    
    if (paymentStatus == 'completed') {
      // Payment successful, proceed with eSIM activation
      showSuccessMessage();
    } else {
      // Payment pending or failed
      showPaymentStatusMessage(paymentStatus);
    }
  } else {
    // Handle error
    print('Error: ${response.body}');
  }
}
```

### Örnek Mobil Uygulama Kodu (Swift - iOS)

```swift
func createPayment() {
    let url = URL(string: "https://yourdomain.com/api/payment/create-link")!
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.addValue("application/json", forHTTPHeaderField: "Content-Type")
    request.addValue("application/json", forHTTPHeaderField: "Accept")
    
    let parameters: [String: Any] = [
        "package_code": "GL-144_6_15",
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+905551234567",
        "callback_url": "https://yourapp.com/payment/callback"
    ]
    
    request.httpBody = try? JSONSerialization.data(withJSONObject: parameters)
    
    URLSession.shared.dataTask(with: request) { data, response, error in
        if let data = data {
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool,
                   success,
                   let dataObj = json["data"] as? [String: Any],
                   let paymentUrl = dataObj["payment_url"] as? String,
                   let merchantReferenceId = dataObj["merchant_reference_id"] as? String {
                    
                    // Save merchant_reference_id for later verification
                    self.savePaymentReference(merchantReferenceId)
                    
                    // Open payment URL in WebView or Safari
                    DispatchQueue.main.async {
                        if let url = URL(string: paymentUrl) {
                            UIApplication.shared.open(url)
                        }
                    }
                }
            } catch {
                print("Error parsing JSON: \(error)")
            }
        }
    }.resume()
}

func verifyPayment(merchantReferenceId: String) {
    let url = URL(string: "https://yourdomain.com/api/payment/verify")!
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.addValue("application/json", forHTTPHeaderField: "Content-Type")
    request.addValue("application/json", forHTTPHeaderField: "Accept")
    
    let parameters: [String: Any] = [
        "merchant_reference_id": merchantReferenceId
    ]
    
    request.httpBody = try? JSONSerialization.data(withJSONObject: parameters)
    
    URLSession.shared.dataTask(with: request) { data, response, error in
        if let data = data {
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool,
                   success,
                   let dataObj = json["data"] as? [String: Any],
                   let status = dataObj["status"] as? String {
                    
                    DispatchQueue.main.async {
                        if status == "completed" {
                            // Payment successful, proceed with eSIM activation
                            self.showSuccessMessage()
                        } else {
                            // Payment pending or failed
                            self.showPaymentStatusMessage(status)
                        }
                    }
                }
            } catch {
                print("Error parsing JSON: \(error)")
            }
        }
    }.resume()
}
```

### Örnek Mobil Uygulama Kodu (Kotlin - Android)

```kotlin
fun createPayment() {
    val url = "https://yourdomain.com/api/payment/create-link"
    val client = OkHttpClient()
    
    val json = JSONObject().apply {
        put("package_code", "GL-144_6_15")
        put("name", "John Doe")
        put("email", "<EMAIL>")
        put("phone", "+905551234567")
        put("callback_url", "https://yourapp.com/payment/callback")
    }
    
    val requestBody = json.toString().toRequestBody("application/json".toMediaType())
    
    val request = Request.Builder()
        .url(url)
        .post(requestBody)
        .header("Content-Type", "application/json")
        .header("Accept", "application/json")
        .build()
    
    client.newCall(request).enqueue(object : Callback {
        override fun onFailure(call: Call, e: IOException) {
            e.printStackTrace()
        }
        
        override fun onResponse(call: Call, response: Response) {
            response.use {
                if (!response.isSuccessful) return
                
                val responseBody = response.body?.string()
                val jsonObject = JSONObject(responseBody)
                
                if (jsonObject.getBoolean("success")) {
                    val data = jsonObject.getJSONObject("data")
                    val paymentUrl = data.getString("payment_url")
                    val merchantReferenceId = data.getString("merchant_reference_id")
                    
                    // Save merchant_reference_id for later verification
                    savePaymentReference(merchantReferenceId)
                    
                    // Open payment URL in WebView or external browser
                    activity?.runOnUiThread {
                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(paymentUrl))
                        startActivity(intent)
                    }
                }
            }
        }
    })
}

fun verifyPayment(merchantReferenceId: String) {
    val url = "https://yourdomain.com/api/payment/verify"
    val client = OkHttpClient()
    
    val json = JSONObject().apply {
        put("merchant_reference_id", merchantReferenceId)
    }
    
    val requestBody = json.toString().toRequestBody("application/json".toMediaType())
    
    val request = Request.Builder()
        .url(url)
        .post(requestBody)
        .header("Content-Type", "application/json")
        .header("Accept", "application/json")
        .build()
    
    client.newCall(request).enqueue(object : Callback {
        override fun onFailure(call: Call, e: IOException) {
            e.printStackTrace()
        }
        
        override fun onResponse(call: Call, response: Response) {
            response.use {
                if (!response.isSuccessful) return
                
                val responseBody = response.body?.string()
                val jsonObject = JSONObject(responseBody)
                
                if (jsonObject.getBoolean("success")) {
                    val data = jsonObject.getJSONObject("data")
                    val status = data.getString("status")
                    
                    activity?.runOnUiThread {
                        if (status == "completed") {
                            // Payment successful, proceed with eSIM activation
                            showSuccessMessage()
                        } else {
                            // Payment pending or failed
                            showPaymentStatusMessage(status)
                        }
                    }
                }
            }
        }
    })
}

void handlePaymentCallback(String url) {
  final uri = Uri.parse(url);
  final params = uri.queryParameters;
  
  final merchantReferenceId = params['merchant_reference_id'];
  final paymentId = params['payment_id'];
  final checkoutId = params['checkout_id'];
  
  if (merchantReferenceId != null) {
    verifyPayment(merchantReferenceId);
  } else {
    print('Error: Missing payment identifiers');
  }
}
```

