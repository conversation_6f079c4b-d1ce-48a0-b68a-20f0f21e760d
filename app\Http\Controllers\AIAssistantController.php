<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\AIAssistantService;
use Illuminate\Support\Facades\Log;

class AIAssistantController extends Controller
{
    protected $aiService;

    public function __construct(AIAssistantService $aiService)
    {
        $this->aiService = $aiService;
    }

    public function createThread()
    {
        try {
            $threadId = $this->aiService->createThread();
            
            if ($threadId) {
                return response()->json([
                    'success' => true,
                    'threadId' => $threadId
                ]);
            } else {
                Log::error('AI Assistant - Thread creation failed');
                return response()->json([
                    'success' => false,
                    'message' => 'Thread creation failed'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('AI Assistant - Thread creation exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred'
            ], 500);
        }
    }

    public function sendMessage(Request $request)
    {
        try {
            $validated = $request->validate([
                'message' => 'required|string',
                'threadId' => 'required|string'
            ]);
            
            $result = $this->aiService->sendMessage(
                $validated['threadId'],
                $validated['message']
            );
            
            if ($result && isset($result['thread_id']) && isset($result['run_id'])) {
                return response()->json([
                    'success' => true,
                    'threadId' => $result['thread_id'],
                    'runId' => $result['run_id']
                ]);
            } else {
                Log::error('AI Assistant - Message sending failed', [
                    'result' => $result
                ]);
                
                return response()->json([
                    'success' => false,
                    'message' => 'Message sending failed'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('AI Assistant - Message sending exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred'
            ], 500);
        }
    }

    public function checkRunStatus(Request $request)
    {
        try {
            $validated = $request->validate([
                'threadId' => 'required|string',
                'runId' => 'required|string'
            ]);
            
            $result = $this->aiService->checkRunStatus(
                $validated['threadId'],
                $validated['runId']
            );
            
            if ($result) {
                return response()->json([
                    'success' => true,
                    'isComplete' => $result['is_complete'],
                    'message' => $result['message'] ?? null
                ]);
            } else {
                Log::error('AI Assistant - Run status check failed');
                
                return response()->json([
                    'success' => false,
                    'message' => 'Run status check failed'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('AI Assistant - Run status check exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred'
            ], 500);
        }
    }
}
