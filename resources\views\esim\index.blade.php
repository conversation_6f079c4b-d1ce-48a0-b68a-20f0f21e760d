@extends('layouts.app')

@section('content')
<div class="container">
    @foreach($packages as $country => $countryPackages)
        <div class="card mb-4">
            <div class="card-header" id="country-{{ Str::slug($country) }}">
                <h2 class="mb-0">
                    <button class="btn btn-link" type="button" data-toggle="collapse" 
                            data-target="#collapse-{{ Str::slug($country) }}">
                        {{ $country }}
                    </button>
                </h2>
            </div>

            <div id="collapse-{{ Str::slug($country) }}" class="collapse">
                <div class="card-body">
                    <div class="row">
                        @foreach($countryPackages as $package)
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ $package['name'] }}</h5>
                                        <p>Data: {{ $package['data_amount'] }}GB</p>
                                        <p>Duration: {{ $package['validity_days'] }} days</p>
                                        <p>Price: ${{ number_format($package['retail_price'], 2) }}</p>
                                        <a href="{{ route('esim.checkout', $package['id']) }}" 
                                           class="btn btn-primary">Buy Now</a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    @endforeach
</div>
@endsection