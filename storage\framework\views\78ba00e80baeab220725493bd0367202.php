<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-16921653689"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'AW-16921653689');
    </script>
    
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="description" content="Vox eSIM - Global connectivity with affordable and reliable eSIM plans for travelers worldwide.">
    <meta name="keywords" content="eSIM, travel, data, roaming, international, connectivity">

    <title><?php echo e(config('app.name', 'Vox eSIM')); ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo e(asset('images/esimlogo.png')); ?>" type="image/png">
    <link rel="shortcut icon" href="<?php echo e(asset('images/esimlogo.png')); ?>" type="image/png">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link href="<?php echo e(asset('css/style.css')); ?>" rel="stylesheet">

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <div id="app">
        <!-- Header -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
            <div class="container">
                <!-- Logo -->
                <a class="navbar-brand" href="<?php echo e(url('/')); ?>">
                    <span>Vox eSIM</span>
                </a>

                <!-- Mobile Menu Toggle -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Main Navigation -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(url('/')); ?>"><?php echo e(__('messages.home')); ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#best-sellers"><?php echo e(__('messages.best_selling_plans')); ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#how-it-works"><?php echo e(__('messages.how_it_works')); ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#countries"><?php echo e(__('messages.countries')); ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#support"><?php echo e(__('messages.need_help')); ?></a>
                        </li>
                    </ul>

                    <!-- Right Side Navigation -->
                    <ul class="navbar-nav ms-auto">
                   

                        <?php if(auth()->guard()->guest()): ?>
                            <?php if(Route::has('login')): ?>
                                <li class="nav-item">
                                    <a class="nav-link login-btn" href="<?php echo e(route('login')); ?>"><?php echo e(__('messages.login')); ?></a>
                                </li>
                            <?php endif; ?>
                        <?php else: ?>
                            <li class="nav-item dropdown user-dropdown">
                                <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    <i class="fas fa-user-circle me-1"></i> <?php echo e(explode('@', Auth::user()->email)[0]); ?>

                                </a>

                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item" href="<?php echo e(route('profile.dashboard')); ?>">
                                        <i class="fas fa-tachometer-alt me-2"></i> <?php echo e(__('messages.profile')); ?>

                                    </a>
                                    
                                    <a class="dropdown-item" href="<?php echo e(route('logout')); ?>"
                                       onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                        <i class="fas fa-sign-out-alt me-2"></i> <?php echo e(__('messages.logout')); ?>

                                    </a>

                                    <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
                                        <?php echo csrf_field(); ?>
                                    </form>
                                </div>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main>
            <?php echo $__env->yieldContent('content'); ?>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="row">
                    <!-- Logo and Description -->
                    <div class="col-lg-4 mb-4">
                        <div class="footer-logo">
                            <img src="<?php echo e(asset('images/esimlogo.png')); ?>" alt="Vox eSIM" class="img-fluid">
                        </div>
                        <p>Vox eSIM provides affordable and reliable eSIM solutions for travelers worldwide. Stay connected wherever you go with our global coverage.</p>
                        <div class="app-stores mt-3">
                            <a href="#" class="app-store-btn apple-btn me-3" data-bs-toggle="modal" data-bs-target="#comingSoonModal">
                                <i class="fab fa-apple"></i> App Store
                            </a>
                            <a href="#" class="app-store-btn google-btn" data-bs-toggle="modal" data-bs-target="#comingSoonModal">
                                <i class="fab fa-google-play"></i> Google Play
                            </a>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="col-lg-2 col-md-4 mb-4">
                        <h5>Quick Links</h5>
                        <ul class="footer-links">
                            <li><a href="<?php echo e(url('/')); ?>">Home</a></li>
                            <li><a href="#best-sellers">Best Sellers</a></li>
                            <li><a href="#how-it-works">How It Works</a></li>
                            <li><a href="#countries">Countries</a></li>
                        </ul>
                    </div>

                    <!-- Support -->
                    <div class="col-lg-3 col-md-4 mb-4">
                        <h5>Support</h5>
                        <ul class="footer-links">
                            <li><a href="#">FAQ</a></li>
                            <li><a href="#">Contact Us</a></li>
                            <li><a href="#">Privacy Policy</a></li>
                            <li><a href="#">Terms of Service</a></li>
                        </ul>
                    </div>

                    <!-- Contact -->
                    <div class="col-lg-3 col-md-4 mb-4">
                        <h5>Contact Us</h5>
                        <ul class="footer-links">
                            <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                            <li><i class="fas fa-map-marker-alt me-2"></i> Sepapaja 6, Tallinn 15551, Estonia</li>                                                      
                        </ul>
                    </div>
                </div>

                <!-- Copyright -->
                <div class="footer-bottom">
                    <p>&copy; <?php echo e(date('Y')); ?> Vox eSIM. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="<?php echo e(asset('js/main.js')); ?>"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
    <!-- Coming Soon Modal -->
    <div class="modal fade" id="comingSoonModal" tabindex="-1" aria-labelledby="comingSoonModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="comingSoonModalLabel">Coming Soon!</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <div class="coming-soon-icon mb-3">
                        <img src="<?php echo e(asset('images/logo.png')); ?>" alt="Vox eSIM" class="img-fluid" style="max-height: 60px;">
                    </div>
                    <h4>Our mobile app is coming soon!</h4>
                    <p class="mb-0">We're currently only selling through our website, but our mobile app will be available in the near future. Stay tuned for updates!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Got it</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Cookie Consent Popup -->
    <div id="cookie-consent" class="cookie-consent">
        <div class="cookie-consent-header">
            <div class="cookie-consent-icon">
                <i class="fas fa-cookie-bite"></i>
            </div>
            <h4>Çerez Kullanımı</h4>
        </div>
        <div class="cookie-consent-message">
            <p>Size daha iyi bir deneyim sunmak için çerezleri kullanıyoruz. Devam ederek çerez kullanımını kabul etmiş olursunuz.</p>
        </div>
        <div class="cookie-consent-actions">
            <button id="accept-cookies" class="cookie-consent-btn cookie-accept-btn">Kabul Et</button>
            <button id="decline-cookies" class="cookie-consent-btn cookie-decline-btn">Reddet</button>
            <a href="<?php echo e(route('cookie.policy')); ?>" class="cookie-settings-btn">Detaylar</a>
        </div>
    </div>

    <!-- CSS ve JS dosyalarını ekleyin -->
    <link rel="stylesheet" href="<?php echo e(asset('css/cookie-consent.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/ai-assistant.css')); ?>">
    <script src="<?php echo e(asset('js/cookie-consent.js')); ?>"></script>
    <script src="<?php echo e(asset('js/ai-assistant.js')); ?>"></script>
</body>
</html>














<?php /**PATH C:\Users\<USER>\Documents\GitHub\vox-esim-web\resources\views/layouts/app.blade.php ENDPATH**/ ?>