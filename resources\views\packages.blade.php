<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>{{ $countryCode }} Paketleri</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-5">
    <a href="{{ route('home') }}" class="btn btn-secondary mb-3">← Ülke Listesi</a>
    <h2 class="mb-4">{{ $countryCode }} eSIM Paketleri</h2>

    @if(count($packages))
        <table class="table table-bordered">
            <thead class="table-light">
            <tr>
                <th>Paket Adı</th>
                <th>Data Miktarı</th>
                <th>Süre (Gün)</th>
                <th>Fiyat</th>
            </tr>
            </thead>
            <tbody>
            @foreach ($packages as $package)
                <tr>
                    <td>{{ $package['packageName'] }}</td>
                    <td>{{ $package['dataVolume'] }}</td>
                    <td>{{ $package['validityDays'] }}</td>
                    <td>${{ number_format(ceil($package['retailPrice'] / 10000) - 0.01, 2) }}</td>
                </tr>
            @endforeach
            </tbody>
        </table>
    @else
        <p class="text-danger">Bu ülke için paket bulunamadı.</p>
    @endif
</div>
</body>
</html>
