<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Stripe\Stripe;
use Stripe\Checkout\Session;

class WebPaymentController extends Controller
{
    public function createCheckoutSession(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));

        try {
            $amount = $request->input('amount');
            $currency = $request->input('currency');
            $description = $request->input('description');
            $metadata = $request->input('metadata', []);

            $session = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => $currency,
                        'product_data' => [
                            'name' => $description,
                            'metadata' => $metadata,
                        ],
                        'unit_amount' => $amount, // Cent cinsinden (örneğin, 999 için €9.99)
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => 'https://your-app.com/success?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => 'https://your-app.com/cancel',
            ]);

            return response()->json([
                'checkout_url' => $session->url,
            ]);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Sunucu hatası'], 500);
        }
    }

    // Ödeme sonucunu kontrol etmek için (isteğe bağlı)
    public function verifyCheckoutSession(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));

        try {
            $sessionId = $request->query('session_id');
            $session = Session::retrieve($sessionId);

            if ($session->payment_status === 'paid') {
                return response()->json(['status' => 'success']);
            } else {
                return response()->json(['status' => 'failed']);
            }
        } catch (\Stripe\Exception\ApiErrorException $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Sunucu hatası'], 500);
        }
    }
}