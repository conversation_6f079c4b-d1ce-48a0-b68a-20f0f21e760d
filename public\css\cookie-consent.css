.cookie-consent {
    position: fixed;
    bottom: 20px;
    right: 20px;
    max-width: 350px;
    background-color: #fff;
    color: #333;
    padding: 15px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transform: translateY(150%);
    transition: transform 0.4s ease-in-out;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
}

.cookie-consent.show {
    transform: translateY(0);
}

.cookie-consent-message {
    margin-bottom: 15px;
}

.cookie-consent-message h4 {
    margin-top: 0;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
}

.cookie-consent-message p {
    margin-bottom: 0;
    color: #555;
    line-height: 1.4;
    font-size: 13px;
}

.cookie-consent-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.cookie-consent-btn {
    padding: 8px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    font-size: 13px;
    transition: all 0.2s;
    text-align: center;
}

.cookie-accept-btn {
    background-color: #3498db;
    color: white;
    flex: 1;
}

.cookie-accept-btn:hover {
    background-color: #2980b9;
}

.cookie-decline-btn {
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
    flex: 1;
}

.cookie-decline-btn:hover {
    background-color: #e9ecef;
}

.cookie-settings-btn {
    color: #3498db;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    transition: color 0.2s;
    margin-left: auto;
}

.cookie-settings-btn:hover {
    color: #2980b9;
    text-decoration: underline;
}

.cookie-consent-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.cookie-consent-icon {
    margin-right: 10px;
    font-size: 18px;
    color: #3498db;
}

@media (max-width: 576px) {
    .cookie-consent {
        bottom: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

