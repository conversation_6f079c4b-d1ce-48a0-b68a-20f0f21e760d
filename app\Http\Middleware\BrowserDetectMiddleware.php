<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

// Fallback definition for t() function if it doesn't exist
if (!function_exists('t')) {
    /**
     * Translate the given message.
     *
     * @param  string|null  $key
     * @param  array  $replace
     * @param  string|null  $locale
     * @return string|array|null
     */
    function t($key = null, $replace = [], $locale = null)
    {
        return \App\Helpers\TranslationHelper::trans($key, $replace, $locale);
    }
}

class BrowserDetectMiddleware
{
    /**
     * Desteklenen diller
     */
    protected $supportedLanguages = [
        'en', 'tr', 'de', 'es', 'fr', 'it', 'ja', 'ko', 'zh', 'ru', 'pt', 'ar'
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Force English locale
        Session::put('locale', 'en');
        App::setLocale('en');
        
        return $next($request);
        
        /* Original code commented out
        // Eğer session'da dil zaten ayarlanmışsa, onu kullan
        if (Session::has('locale')) {
            $locale = Session::get('locale');
            App::setLocale($locale);
            return $next($request);
        }

        // Tarayıcı dilini al
        $browserLang = 'en'; // Varsayılan olarak İngilizce

        if ($request->server('HTTP_ACCEPT_LANGUAGE')) {
            $acceptLanguage = $request->server('HTTP_ACCEPT_LANGUAGE');
            $languages = explode(',', $acceptLanguage);

            foreach ($languages as $language) {
                // Dil ve kalite değerini ayır (örn: en-US;q=0.8)
                $langParts = explode(';', $language);
                $langCode = trim($langParts[0]);

                // Dil kodunu 2 karaktere indir (örn: en-US -> en)
                if (strpos($langCode, '-') !== false) {
                    $langCode = substr($langCode, 0, strpos($langCode, '-'));
                }

                // Eğer desteklenen bir dil ise, onu kullan
                if (in_array($langCode, $this->supportedLanguages)) {
                    $browserLang = $langCode;
                    break;
                }
            }
        }

        // Kullanıcının konumuna göre dil ayarı (Varsayılan olarak Türkçe)
        // Bu örnekte, Türkiye'de olduğunuz için varsayılan olarak Türkçe seçilecek
        $clientIp = $request->ip();

        // Yerel geliştirme ortamında veya Türkiye'den erişim yapılıyorsa
        if ($clientIp === '127.0.0.1' || $request->server('SERVER_NAME') === 'localhost') {
            // Yerel geliştirme ortamında Türkçe olarak ayarla
            $browserLang = 'tr';
        }

        // Log browser language detection
        \Log::info('Browser language detected', [
            'browser_lang' => $browserLang,
            'supported_languages' => $this->supportedLanguages,
            'HTTP_ACCEPT_LANGUAGE' => $request->server('HTTP_ACCEPT_LANGUAGE'),
            'client_ip' => $clientIp
        ]);

        // If language is supported, use it
        if (in_array($browserLang, $this->supportedLanguages)) {
            Session::put('locale', $browserLang);
            App::setLocale($browserLang);

            \Log::info('Browser language is supported and set', [
                'locale' => $browserLang
            ]);
        } else {
            // If not supported, use English as default
            Session::put('locale', 'en');
            App::setLocale('en');

            \Log::info('Browser language not supported, default language set', [
                'browser_lang' => $browserLang,
                'default_locale' => 'en'
            ]);
        }
        */
    }
}


