import paramiko

# GitHub bilgileri
github_user = "elsurnite"
github_token = "****************************************"
repo_url = f"https://{github_user}:{github_token}@github.com/{github_user}/vox-esim-web.git"
branch = "main"

# Sunucu bilgileri
server_ip = "*************"
server_user = "root"
server_pass = "Plex123"
target_dir = "/home/<USER>/web/voxesim.com/public_html"

# SSH komut bloğu
commands = f"""
cd {target_dir}

# Remote URL ayarlanıyor (eski varsa güncelleniyor)
git remote remove origin 2>/dev/null
git remote add origin {repo_url}

echo "🔁 GitHub'dan güncellemeler çekiliyor..."
git fetch origin {branch}

echo "🧠 Değişiklikler:"
git diff --name-status HEAD origin/{branch}

echo "💥 Reset başlıyor (tüm dosyalar güncellenecek)..."
git reset --hard origin/{branch}
"""

# SSH ile çalıştırma
def deploy_full_update():
    print(f"🔐 Sunucuya bağlanılıyor: {server_ip}")
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        ssh.connect(server_ip, username=server_user, password=server_pass)
        stdin, stdout, stderr = ssh.exec_command(commands)

        print("✅ --- KOMUT ÇIKTISI ---")
        print(stdout.read().decode())
        print("❌ --- HATA VARSA ---")
        print(stderr.read().decode())

        ssh.close()
        print("✅ Dağıtım tamamlandı.")
    except Exception as e:
        print(f"🚨 SSH bağlantı hatası: {e}")

deploy_full_update()
