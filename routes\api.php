<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WebPaymentController;
use App\Http\Controllers\Api\PaymentController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Existing web payment routes
Route::post('/create-payment-intent', [WebPaymentController::class, 'createPaymentIntent']);
Route::post('/confirm-payment', [WebPaymentController::class, 'confirmPayment']);

// New Rapyd payment API routes
Route::post('/payment/create-link', [PaymentController::class, 'createPaymentLink']);
Route::post('/payment/verify', [PaymentController::class, 'verifyPayment'])
    ->middleware('validate.payment.params');
    
// AI Assistant API Routes
Route::prefix('ai-assistant')->group(function () {
    Route::post('/create-thread', [AIAssistantController::class, 'createThread']);
    Route::post('/send-message', [AIAssistantController::class, 'sendMessage']);
    Route::post('/check-status', [AIAssistantController::class, 'checkRunStatus']);
});
