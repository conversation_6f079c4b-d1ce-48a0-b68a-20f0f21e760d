<?php $__env->startSection('content'); ?>
<!-- Country Header Section -->
<section class="py-4 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Home</a></li>
                <li class="breadcrumb-item"><a href="#countries">Countries</a></li>
                <li class="breadcrumb-item active"><?php echo e($location['name']); ?></li>
            </ol>
        </nav>
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mt-2"><?php echo e($location['name']); ?> eSIM Plans</h1>
                <p class="lead">Choose the perfect eSIM plan for your trip to <?php echo e($location['name']); ?></p>
            </div>
            <div class="col-md-4 text-end">
                <div class="country-flag-large bg-white shadow-sm rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 100px; height: 100px;">
                    <img src="https://flagcdn.com/96x72/<?php echo e(strtolower($code)); ?>.png" alt="<?php echo e($location['name']); ?> flag" style="max-width: 80%; max-height: 80%; object-fit: contain;">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Packages Section -->
<section class="py-5">
    <div class="container">
        <!-- Filter Options -->
        <div class="package-filters mb-4">
            <div class="row g-2">
                <div class="col-md-3">
                    <select class="form-select" id="dataFilter">
                        <option value="all">All Data Plans</option>
                        <option value="1GB">1GB & Less</option>
                        <option value="5GB">1GB - 5GB</option>
                        <option value="10GB">5GB - 10GB</option>
                        <option value="unlimited">10GB+</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="durationFilter">
                        <option value="all">All Durations</option>
                        <option value="7">7 Days & Less</option>
                        <option value="15">8-15 Days</option>
                        <option value="30">16-30 Days</option>
                        <option value="31">30+ Days</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="priceFilter">
                        <option value="all">All Prices</option>
                        <option value="budget">Budget ($0-$15)</option>
                        <option value="standard">Standard ($15-$30)</option>
                        <option value="premium">Premium ($30+)</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-primary w-100" id="resetFilters">Reset Filters</button>
                </div>
            </div>
        </div>

        <!-- Package Display Toggle -->
        <div class="package-display-toggle mb-4">
            <div class="btn-group w-100">
                <button class="btn btn-primary active" id="showCountryPackages"><?php echo e($location['name']); ?> Specific Plans</button>
                <button class="btn btn-outline-primary" id="showAllPackages">All Valid Plans for <?php echo e($location['name']); ?></button>
            </div>
        </div>

        <!-- Country Specific Packages -->
        <div id="countryPackagesContainer">
            <?php if(count($countryPackages)): ?>
                <div class="row">
                    <?php $__currentLoopData = $countryPackages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-4 mb-4 animate-fade-in">
                            <div class="package-card">
                                <div class="card-header text-center">
                                    <h4><?php echo e($package['name']); ?></h4>
                                </div>
                                <div class="card-body">
                                    <div class="package-feature">
                                        <i class="fas fa-wifi"></i>
                                        <span>Data: <?php echo e($package['data']); ?> <small class="text-muted">(<?php echo e(number_format($package['price_per_gb'], 2)); ?> $/GB)</small></span>
                                    </div>
                                    <div class="package-feature">
                                        <i class="fas fa-calendar-alt"></i>
                                        <span>Validity: <?php echo e($package['validity']); ?> days</span>
                                    </div>
                                    <div class="package-feature">
                                        <i class="fas fa-signal"></i>
                                        <span>Network: 
                                            <?php
                                                $speed = $package['speed'] ?? 'Premium Quality';
                                                // Eğer 3G/4G/5G formatında ise, sadece 4G/5G göster
                                                if ($speed === '3G/4G/5G') {
                                                    echo '4G/5G';
                                                } 
                                                // Diğer durumlarda olduğu gibi göster
                                                else {
                                                    echo $speed;
                                                }
                                            ?>
                                        </span>
                                    </div>
                                    <div class="package-feature">
                                        <i class="fas fa-globe"></i>
                                        <div class="coverage-container">
                                            <span><?php echo e(count($package['locationCodes'])); ?> countries</span>
                                            <a href="#" class="coverage-details-link" 
                                               data-bs-toggle="modal" data-bs-target="#locationsModal" 
                                               data-locations="<?php echo e(json_encode($package['locationNames'])); ?>"
                                               data-location-codes="<?php echo e(json_encode($package['locationCodesForFlags'])); ?>">
                                                <i class="fas fa-info-circle me-1"></i> View details
                                            </a>
                                        </div>
                                    </div>
                                    <div class="package-price">
                                        $<?php echo e($package['price']); ?> <small>USD</small>
                                    </div>
                                    <a href="<?php echo e(route('esim.checkout', ['packageCode' => $package['packageCode']])); ?>"
                                       class="btn btn-primary w-100">
                                        Buy Now
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No <?php echo e($location['name']); ?> specific eSIM packages available at the moment. Please check other valid plans for this country.
                </div>
            <?php endif; ?>
        </div>

        <!-- All Valid Packages -->
        <div id="allPackagesContainer" style="display: none;">
            <?php if(count($allValidPackages)): ?>
                <div class="row">
                    <?php $__currentLoopData = $allValidPackages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-4 mb-4 animate-fade-in">
                            <div class="package-card">
                                <div class="card-header text-center">
                                    <h4><?php echo e($package['name']); ?></h4>
                                </div>
                                <div class="card-body">
                                    <div class="package-feature">
                                        <i class="fas fa-wifi"></i>
                                        <span>Data: <?php echo e($package['data']); ?> <small class="text-muted">(<?php echo e(number_format($package['price_per_gb'], 2)); ?> $/GB)</small></span>
                                    </div>
                                    <div class="package-feature">
                                        <i class="fas fa-calendar-alt"></i>
                                        <span>Validity: <?php echo e($package['validity']); ?> days</span>
                                    </div>
                                    <div class="package-feature">
                                        <i class="fas fa-signal"></i>
                                        <span>Network: 
                                            <?php
                                                $speed = $package['speed'] ?? 'Premium Quality';
                                                // Eğer 3G/4G/5G formatında ise, sadece 4G/5G göster
                                                if ($speed === '3G/4G/5G') {
                                                    echo '4G/5G';
                                                } 
                                                // Diğer durumlarda olduğu gibi göster
                                                else {
                                                    echo $speed;
                                                }
                                            ?>
                                        </span>
                                    </div>
                                    <div class="package-feature">
                                        <i class="fas fa-globe"></i>
                                        <div class="coverage-container">
                                            <span><?php echo e(count($package['locationCodes'])); ?> countries</span>
                                            <a href="#" class="coverage-details-link" 
                                               data-bs-toggle="modal" data-bs-target="#locationsModal" 
                                               data-locations="<?php echo e(json_encode($package['locationNames'])); ?>"
                                               data-location-codes="<?php echo e(json_encode($package['locationCodesForFlags'])); ?>">
                                                <i class="fas fa-info-circle me-1"></i> View details
                                            </a>
                                        </div>
                                    </div>
                                    <div class="package-price">
                                        $<?php echo e($package['price']); ?> <small>USD</small>
                                    </div>
                                    <a href="<?php echo e(route('esim.checkout', ['packageCode' => $package['packageCode']])); ?>"
                                       class="btn btn-primary w-100">
                                        Buy Now
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No eSIM packages available for <?php echo e($location['name']); ?> at the moment. Please check back later or explore other countries.
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Locations Modal -->
<div class="modal fade" id="locationsModal" tabindex="-1" aria-labelledby="locationsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="locationsModalLabel">Coverage Countries</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Arama kutusu -->
                <div class="input-group mb-3">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="locationSearchInput" placeholder="Search countries...">
                </div>
                
                <!-- Ülke listesi -->
                <ul id="locationsList" class="list-group">
                    <!-- Locations will be populated here by JavaScript -->
                </ul>
                
                <!-- Sonuç bulunamadı mesajı -->
                <div id="noLocationsFound" class="alert alert-info mt-3" style="display: none;">
                    No countries found matching your search.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Paket görüntüleme geçişi
    const showCountryPackagesBtn = document.getElementById('showCountryPackages');
    const showAllPackagesBtn = document.getElementById('showAllPackages');
    const countryPackagesContainer = document.getElementById('countryPackagesContainer');
    const allPackagesContainer = document.getElementById('allPackagesContainer');
    
    showCountryPackagesBtn.addEventListener('click', function() {
        countryPackagesContainer.style.display = 'block';
        allPackagesContainer.style.display = 'none';
        showCountryPackagesBtn.classList.add('active');
        showCountryPackagesBtn.classList.remove('btn-outline-primary');
        showCountryPackagesBtn.classList.add('btn-primary');
        showAllPackagesBtn.classList.remove('active');
        showAllPackagesBtn.classList.remove('btn-primary');
        showAllPackagesBtn.classList.add('btn-outline-primary');
    });
    
    showAllPackagesBtn.addEventListener('click', function() {
        countryPackagesContainer.style.display = 'none';
        allPackagesContainer.style.display = 'block';
        showAllPackagesBtn.classList.add('active');
        showAllPackagesBtn.classList.remove('btn-outline-primary');
        showAllPackagesBtn.classList.add('btn-primary');
        showCountryPackagesBtn.classList.remove('active');
        showCountryPackagesBtn.classList.remove('btn-primary');
        showCountryPackagesBtn.classList.add('btn-outline-primary');
    });
    
    // Locations modal için event listener
    const locationsModal = document.getElementById('locationsModal');
    const locationsList = document.getElementById('locationsList');
    const locationSearchInput = document.getElementById('locationSearchInput');
    const noLocationsFound = document.getElementById('noLocationsFound');
    
    let allLocations = []; // Tüm lokasyonları saklayacak dizi
    let allLocationCodes = []; // Tüm lokasyon kodlarını saklayacak dizi
    
    document.querySelectorAll('.coverage-details-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const locations = JSON.parse(this.getAttribute('data-locations'));
            const locationCodes = JSON.parse(this.getAttribute('data-location-codes'));
            
            // Lokasyonları ve kodları sakla
            allLocations = locations;
            allLocationCodes = locationCodes;
            
            // Lokasyonları ve kodları birleştir (sıralama için)
            const combinedData = allLocations.map((location, index) => {
                return {
                    name: location,
                    code: allLocationCodes[index] || null
                };
            });
            
            // Alfabetik sırala
            combinedData.sort((a, b) => a.name.localeCompare(b.name));
            
            // Sıralanmış verileri ayır
            allLocations = combinedData.map(item => item.name);
            allLocationCodes = combinedData.map(item => item.code);
            
            // Lokasyonları listele
            displayLocations(allLocations, allLocationCodes);
        });
    });
    
    // Arama işlevi
    locationSearchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        if (searchTerm.trim() === '') {
            // Arama terimi boşsa tüm lokasyonları göster
            displayLocations(allLocations, allLocationCodes);
            return;
        }
        
        // Arama terimine göre filtrele
        const filteredLocations = [];
        const filteredCodes = [];
        
        allLocations.forEach((location, index) => {
            if (location.toLowerCase().includes(searchTerm)) {
                filteredLocations.push(location);
                filteredCodes.push(allLocationCodes[index]);
            }
        });
        
        // Filtrelenmiş lokasyonları göster
        displayLocations(filteredLocations, filteredCodes);
    });
    
    // Lokasyonları listele
    function displayLocations(locations, codes) {
        // Listeyi temizle
        locationsList.innerHTML = '';
        
        if (locations.length === 0) {
            // Sonuç yoksa mesaj göster
            noLocationsFound.style.display = 'block';
            return;
        }
        
        // Sonuç varsa mesajı gizle
        noLocationsFound.style.display = 'none';
        
        // Lokasyonları listele
        locations.forEach((location, index) => {
            const code = codes[index];
            const li = document.createElement('li');
            li.className = 'list-group-item d-flex align-items-center';
            
            // Bayrak ekle (eğer kod varsa)
            if (code) {
                const flag = document.createElement('img');
                flag.src = `https://flagcdn.com/24x18/${code}.png`;
                flag.alt = `${location} flag`;
                flag.className = 'me-2';
                flag.style.width = '24px';
                flag.style.height = '18px';
                li.appendChild(flag);
            }
            
            // Lokasyon adını ekle
            const span = document.createElement('span');
            span.textContent = location;
            li.appendChild(span);
            
            locationsList.appendChild(li);
        });
    }
    
    // Filtreleme işlevleri
    const dataFilter = document.getElementById('dataFilter');
    const durationFilter = document.getElementById('durationFilter');
    const priceFilter = document.getElementById('priceFilter');
    const resetFiltersBtn = document.getElementById('resetFilters');
    
    // Tüm paket kartlarını seç
    const packageCards = document.querySelectorAll('.package-card');
    
    // Filtreleme fonksiyonu
    function applyFilters() {
        const dataValue = dataFilter.value;
        const durationValue = durationFilter.value;
        const priceValue = priceFilter.value;
        
        // Her iki container'daki paketleri filtrele
        filterPackages(countryPackagesContainer, dataValue, durationValue, priceValue);
        filterPackages(allPackagesContainer, dataValue, durationValue, priceValue);
    }
    
    // Belirli bir container içindeki paketleri filtrele
    function filterPackages(container, dataValue, durationValue, priceValue) {
        const packages = container.querySelectorAll('.package-card');
        
        packages.forEach(pkg => {
            const parent = pkg.parentElement;
            
            // Veri miktarını al
            const dataText = pkg.querySelector('.package-feature:nth-child(1) span').textContent;
            const dataMatch = dataText.match(/(\d+(\.\d+)?)\s*GB/);
            const dataAmount = dataMatch ? parseFloat(dataMatch[1]) : 0;
            
            // Geçerlilik süresini al
            const validityText = pkg.querySelector('.package-feature:nth-child(2) span').textContent;
            const validityMatch = validityText.match(/(\d+)\s*days/);
            const validityDays = validityMatch ? parseInt(validityMatch[1]) : 0;
            
            // Fiyatı al
            const priceText = pkg.querySelector('.package-price').textContent;
            const priceMatch = priceText.match(/\$(\d+(\.\d+)?)/);
            const price = priceMatch ? parseFloat(priceMatch[1]) : 0;
            
            // Veri filtresini uygula
            let showByData = true;
            if (dataValue === '1GB') {
                showByData = dataAmount <= 1;
            } else if (dataValue === '5GB') {
                showByData = dataAmount > 1 && dataAmount <= 5;
            } else if (dataValue === '10GB') {
                showByData = dataAmount > 5 && dataAmount <= 10;
            } else if (dataValue === 'unlimited') {
                showByData = dataAmount > 10;
            }
            
            // Süre filtresini uygula
            let showByDuration = true;
            if (durationValue === '7') {
                showByDuration = validityDays <= 7;
            } else if (durationValue === '15') {
                showByDuration = validityDays > 7 && validityDays <= 15;
            } else if (durationValue === '30') {
                showByDuration = validityDays > 15 && validityDays <= 30;
            } else if (durationValue === '31') {
                showByDuration = validityDays > 30;
            }
            
            // Fiyat filtresini uygula
            let showByPrice = true;
            if (priceValue === 'budget') {
                showByPrice = price <= 15;
            } else if (priceValue === 'standard') {
                showByPrice = price > 15 && price <= 30;
            } else if (priceValue === 'premium') {
                showByPrice = price > 30;
            }
            
            // Tüm filtrelere uyuyorsa göster, aksi halde gizle
            if (showByData && showByDuration && showByPrice) {
                parent.style.display = 'block';
            } else {
                parent.style.display = 'none';
            }
        });
        
        // Görünür paket var mı kontrol et
        const visiblePackages = Array.from(packages).filter(pkg => 
            pkg.parentElement.style.display !== 'none'
        );
        
        // Uyarı mesajını göster/gizle
        const alertElement = container.querySelector('.alert');
        if (alertElement) {
            if (visiblePackages.length === 0 && packages.length > 0) {
                alertElement.style.display = 'block';
                alertElement.textContent = 'No packages match your filter criteria.';
            } else {
                alertElement.style.display = packages.length === 0 ? 'block' : 'none';
            }
        }
    }
    
    // Filtre değişikliklerini dinle
    dataFilter.addEventListener('change', applyFilters);
    durationFilter.addEventListener('change', applyFilters);
    priceFilter.addEventListener('change', applyFilters);
    
    // Filtreleri sıfırla
    resetFiltersBtn.addEventListener('click', function() {
        dataFilter.value = 'all';
        durationFilter.value = 'all';
        priceFilter.value = 'all';
        applyFilters();
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>














<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\vox-esim-web\resources\views/country.blade.php ENDPATH**/ ?>