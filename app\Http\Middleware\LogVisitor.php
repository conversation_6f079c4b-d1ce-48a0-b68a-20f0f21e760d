<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\VisitorLog;
use Illuminate\Support\Facades\Auth;

class LogVisitor
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Statik dosyalar, botlar veya health check isteklerini loglamamak için kontrol
        if (!$this->shouldLogRequest($request)) {
            return $next($request);
        }

        // Ziyaretçi bilgilerini kaydet
        VisitorLog::create([
            'ip_address' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'referer' => $request->header('referer'),
            'user_id' => Auth::id(),
        ]);

        return $next($request);
    }

    /**
     * Determine if the request should be logged.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    private function shouldLogRequest(Request $request)
    {
        // Statik dosyaları loglama
        $path = $request->path();
        if (preg_match('/\.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$/i', $path)) {
            return false;
        }

        // API isteklerini loglama (isteğe bağlı)
        if ($request->is('api/*')) {
            return false;
        }

        return true;
    }
}