<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdatePaymentsTableForStatusAndOrderNo extends Migration
{
    public function up()
    {
        Schema::table('payments', function (Blueprint $table) {
            // status alanını enum olarak güncelle (mevcutsa)
            $table->string('status')->default('pending')->change();
            // order_no alanı ekle
            $table->string('order_no')->nullable()->after('status');
        });
    }

    public function down()
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->string('status')->default('pending')->change();
            $table->dropColumn('order_no');
        });
    }
}