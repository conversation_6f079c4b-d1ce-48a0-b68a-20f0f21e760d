<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class EsimService
{
    private $accessCode;
    private $apiUrl;

    public function __construct()
    {
        $this->accessCode = env('ESIM_ACCESS_CODE');
        $this->apiUrl = env('ESIM_API_URL', 'https://api.esimaccess.com');
    }

    public function getLocations()
    {
        try {
            $response = Http::withHeaders([
                'RT-AccessCode' => $this->accessCode
            ])->post($this->apiUrl . '/api/v1/open/location/list', []);

            if (!$response->successful()) {
                Log::error('Location List Error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return collect([]);
            }

            return collect($response->json()['obj'] ?? []);
        } catch (\Exception $e) {
            Log::error('Location List Exception', [
                'message' => $e->getMessage()
            ]);
            return collect([]);
        }
    }

    public function getPackagesByCountry($countryCode)
    {
        try {
            $response = Http::withHeaders([
                'RT-AccessCode' => $this->accessCode
            ])->get($this->apiUrl . '/api/v1/open/package/list');

            if (!$response->successful()) {
                Log::error('Package List Error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return collect([]);
            }

            return collect($response->json()['obj']['packages'] ?? [])
                ->filter(function ($package) use ($countryCode) {
                    return $package['countryCode'] === $countryCode;
                })
                ->sortBy('retail_price');
        } catch (\Exception $e) {
            Log::error('Package List Exception', [
                'message' => $e->getMessage()
            ]);
            return collect([]);
        }
    }

    // Test için balance sorgulama methodu ekleyelim
    public function checkBalance()
    {
        try {
            $response = Http::withHeaders([
                'RT-AccessCode' => $this->accessCode
            ])->post($this->apiUrl . '/api/v1/open/balance/query', []);

            Log::info('Balance Check Response', ['data' => $response->json()]);
            return $response->json();
        } catch (\Exception $e) {
            Log::error('Balance Check Error', ['error' => $e->getMessage()]);
            return null;
        }
    }

    public function getUsageDetails($esimTranNo)
    {
        try {
            $response = Http::withHeaders([
                'RT-AccessCode' => $this->accessCode
            ])->post($this->apiUrl . '/api/v1/open/esim/usage/query', [
                'esimTranNoList' => [$esimTranNo]
            ]);

            if (!$response->successful()) {
                Log::error('eSIM Usage Query Error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return null;
            }

            $data = $response->json();
            Log::info('eSIM Usage Query Response', ['data' => $data]);
            
            if (!isset($data['obj']['esimUsageList'][0])) {
                return null;
            }

            $usage = $data['obj']['esimUsageList'][0];
            return [
                'dataUsage' => $this->formatBytes($usage['dataUsage']),
                'totalData' => $this->formatBytes($usage['totalData']),
                'lastUpdateTime' => Carbon::parse($usage['lastUpdateTime'])->format('Y-m-d H:i:s'),
                'usagePercentage' => ($usage['dataUsage'] / $usage['totalData']) * 100
            ];
        } catch (\Exception $e) {
            Log::error('eSIM Usage Query Exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}




