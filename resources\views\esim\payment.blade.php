@extends('layouts.app')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><PERSON><PERSON><PERSON> ile Ödeme Yap</h3>
                </div>
                <div class="card-body text-center">
                    <!-- Ödeme Yöntemi ve Logo -->
                    <div class="mb-4">
                        <img src="https://cryptologos.cc/logos/{{ strtolower($pay_currency) }}-{{ strtolower($pay_currency) }}-logo.png?v=025" alt="{{ strtoupper($pay_currency) }} Logo" style="width: 60px; height: 60px;" class="mb-2">
                        <h4 class="mt-2">{{ strtoupper($pay_currency) }} Ödeme Yöntemi</h4>
                    </div>

                    <!-- <PERSON><PERSON><PERSON> -->
                    <div class="alert alert-info text-left">
                        <p class="mb-2"><strong>Adım 1:</strong> Aşağıdaki cüzdan adresine <strong>{{ sprintf('%.8f', $pay_amount) }} {{ strtoupper($pay_currency) }}</strong> gönderin.</p>
                        <p class="mb-2"><strong>Adım 2:</strong> Ödemeyi yalnızca <strong>{{ strtoupper($network) }}</strong> ağı üzerinden yapın.</p>
                        <p class="mb-0"><strong>Adım 3:</strong> Ödeme onaylandığında otomatik olarak yönlendirileceksiniz.</p>
                    </div>

                    <!-- Ödeme Bilgileri -->
                    <div class="mb-4">
                        <p><strong>Ödeme Adresi:</strong></p>
                        <div class="bg-light p-2 rounded">
                            <code>{{ $pay_address }}</code>
                            <button class="btn btn-sm btn-outline-secondary ml-2" onclick="navigator.clipboard.writeText('{{ $pay_address }}')">Kopyala</button>
                        </div>
                    </div>

                    <!-- QR Kod -->
                    <div class="my-4">
                        <p><strong>QR Kodu ile Ödeme:</strong></p>
                        {!! $qr_code !!}
                    </div>

                    <!-- Ek Bilgiler -->
                    <div class="text-left">
                        <p><strong>Ödeme Miktarı:</strong> {{ sprintf('%.8f', $pay_amount) }} {{ strtoupper($pay_currency) }}</p>
                        <p><strong>Ağ:</strong> {{ strtoupper($network) }} (Doğru ağı seçtiğinizden emin olun)</p>
                        <p><strong>Son Geçerlilik:</strong> {{ $expiration_estimate_date }}</p>
                    </div>

                    <!-- Uyarılar -->
                    <div class="alert alert-warning text-left mt-4">
                        <h5 class="alert-heading">Dikkat!</h5>
                        <ul class="mb-0">
                            <li>Yanlış ağa gönderilen ödemeler geri alınamaz.</li>
                            <li>Ağ ücretleri (gas fees) sizin sorumluluğunuzdadır ve ağ yoğunluğuna göre değişir.</li>
                            <li>Ödeme süresi dolmadan işlemi tamamlayın.</li>
                        </ul>
                    </div>

                    <!-- Geri Dön Butonu -->
                    <a href="{{ route('home') }}" class="btn btn-secondary mt-3">Ana Sayfaya Dön</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Kopyalama başarılı olduğunda kullanıcıya feedback vermek için
    document.querySelectorAll('.btn-outline-secondary').forEach(button => {
        button.addEventListener('click', function() {
            this.textContent = 'Kopyalandı!';
            setTimeout(() => this.textContent = 'Kopyala', 2000);
        });
    });
</script>
@endsection