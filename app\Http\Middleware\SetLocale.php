<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

// Fallback definition for t() function if it doesn't exist
if (!function_exists('t')) {
    /**
     * Translate the given message.
     *
     * @param  string|null  $key
     * @param  array  $replace
     * @param  string|null  $locale
     * @return string|array|null
     */
    function t($key = null, $replace = [], $locale = null)
    {
        return \App\Helpers\TranslationHelper::trans($key, $replace, $locale);
    }
}

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Force English locale
        Session::put('locale', 'en');
        App::setLocale('en');
        
        return $next($request);
        
        /* Original code commented out
        if (Session::has('locale')) {
            $locale = Session::get('locale');
            App::setLocale($locale);
        }
        */
    }
}

