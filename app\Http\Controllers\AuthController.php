<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use App\Models\User;
use App\Mail\LoginLink;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class AuthController extends Controller
{
    public function showLoginForm()
    {
        return view('auth.login');
    }
    
    public function sendLoginLink(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);
        
        $email = $request->email;
        
        // Check if user exists, if not create one
        $user = User::firstOrCreate(
            ['email' => $email],
            ['name' => explode('@', $email)[0], 'password' => bcrypt(Str::random(24))]
        );
        
        // Generate a unique token
        $token = Str::random(60);
        
        // Store the token with an expiration time (1 hour)
        DB::table('password_reset_tokens')->updateOrInsert(
            ['email' => $email],
            ['token' => $token, 'created_at' => now()]
        );
        
        try {
            // Send the login link email
            Mail::to($email)->send(new LoginLink($token));
            return back()->with('status', 'We have emailed your login link!');
        } catch (\Exception $e) {
            \Log::error('Failed to send login email: ' . $e->getMessage());
            return back()->with('error', 'Failed to send login email. Please try again later.');
        }
    }
    
    public function loginWithToken($token)
    {
        // Find the token in the database
        $tokenData = DB::table('password_reset_tokens')
            ->where('token', $token)
            ->where('created_at', '>', now()->subHour())
            ->first();
            
        if (!$tokenData) {
            return redirect()->route('login')->with('error', 'This login link is invalid or has expired.');
        }
        
        // Find the user
        $user = User::where('email', $tokenData->email)->first();
        
        if (!$user) {
            return redirect()->route('login')->with('error', 'We could not find a user for this login link.');
        }
        
        // Log the user in
        Auth::login($user);
        
        // Delete the token
        DB::table('password_reset_tokens')->where('email', $user->email)->delete();
        
        return redirect()->route('profile.dashboard');
    }
    
    public function logout()
    {
        Auth::logout();
        return redirect()->route('home');
    }
    
    public function dashboard()
    {
        $user = Auth::user();
        $orders = DB::table('payments')
            ->where('email', $user->email)
            ->whereIn('status', ['sended', 'success', 'completed', 'delivered']) // Sadece başarılı olanları listele
            ->orderBy('created_at', 'desc')
            ->get();
        
        // Process orders data
        foreach ($orders as $order) {
            // Convert created_at strings to Carbon instances
            if (is_string($order->created_at)) {
                $order->created_at = \Carbon\Carbon::parse($order->created_at);
            }
            
            // Set default package_name if not exists
            if (!isset($order->package_name)) {
                // Try to get package name from other fields or set default
                $order->package_name = $order->package ?? $order->product_name ?? 'eSIM Package';
            }
            
            // Ensure status field exists
            if (!isset($order->status)) {
                $order->status = 'pending';
            }
        }
        
        return view('profile.dashboard', compact('user', 'orders'));
    }

    /**
     * Query eSIM details from API
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function queryEsim(Request $request)
    {
        \Log::info('queryEsim method called with request:', [
            'all' => $request->all(),
            'orderNo' => $request->input('orderNo')
        ]);
        
        $request->validate([
            'orderNo' => 'required|string'
        ]);
        
        $orderNo = $request->input('orderNo');
        
        try {
            // API isteği için gerekli bilgileri config'den alıyoruz
            $apiUrl = config('services.esim.api_url', 'https://api.esimaccess.com');
            $accessCode = config('services.esim.access_code', 'c6f300327b064f18803f27c1e6ecd859');
            
            // Log the request
            \Log::info('eSIM Query Request:', [
                'orderNo' => $orderNo,
                'apiUrl' => $apiUrl,
                'accessCode' => $accessCode
            ]);
            
            // API isteği için header ve body hazırlama
            $headers = [
                'Content-Type' => 'application/json',
                'RT-AccessCode' => $accessCode
            ];
            
            $body = [
                'orderNo' => $orderNo,
                'pager' => [
                    'page' => 1,
                    'pageSize' => 10
                ]
            ];
            
            // İstek detaylarını loglama
            \Log::info('eSIM API Request Details:', [
                'url' => $apiUrl . '/api/v1/open/esim/query',
                'method' => 'POST',
                'headers' => $headers,
                'body' => $body
            ]);
            
            // API isteği yapılıyor - Laravel'den API'ye istek atıyoruz
            $response = Http::withHeaders($headers)->post($apiUrl . '/api/v1/open/esim/query', $body);
            
            \Log::info('eSIM Query Response for order ' . $orderNo . ':', [
                'status' => $response->status(),
                'body' => $response->json()
            ]);
            
            if (!$response->successful()) {
                return response()->json([
                    'success' => false,
                    'message' => 'API request failed: ' . $response->status()
                ], 400);
            }
            
            $data = $response->json();
            
            if (!isset($data['obj']['esimList']) || empty($data['obj']['esimList'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'No eSIM found with this order number'
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'data' => $data['obj']['esimList'][0]
            ]);
        } catch (\Exception $e) {
            \Log::error('eSIM query error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while querying eSIM details: ' . $e->getMessage()
            ], 500);
        }
    }
}





